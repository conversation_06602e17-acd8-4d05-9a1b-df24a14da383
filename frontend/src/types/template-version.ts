import { type ChecklistTemplate, type ChecklistItem } from '@/api/template'
import { type StatusButtonGroup, type DefectRule } from './defect-config'
import { type TableViewConfig, type CustomFieldDefinition } from './table-config'

// 模板版本快照
export interface TemplateSnapshot {
  id: string
  templateId: string
  version: string  // 版本号，如 "1.0.0", "1.1.0"
  createdTime: string
  createdBy: string
  description?: string  // 版本描述
  
  // 模板基本信息快照
  template: {
    id: string
    name: string
    type: string
    description: string
    category: string
    tags: string[]
    isActive: boolean
  }
  
  // 检查项快照
  items: ChecklistItem[]
  
  // 配置快照
  config: {
    buttonGroups: StatusButtonGroup[]
    defectRules: DefectRule[]
    tableConfigs: TableViewConfig[]
    customFields: CustomFieldDefinition[]
  }
  
  // 版本元数据
  metadata: {
    itemCount: number
    requiredItemCount: number
    categoryCount: number
    buttonGroupCount: number
    defectRuleCount: number
    hash: string  // 内容哈希，用于检测变更
  }
}

// 检查单与模板版本的关联
export interface ReviewTemplateVersion {
  reviewId: string
  templateId: string
  snapshotId: string
  version: string
  createdTime: string
  
  // 快照数据（嵌入式存储，避免外键依赖）
  snapshot: TemplateSnapshot
}

// 版本比较结果
export interface VersionComparison {
  templateId: string
  currentVersion: string
  targetVersion: string
  
  changes: {
    template: TemplateChange[]
    items: ItemChange[]
    config: ConfigChange[]
  }
  
  summary: {
    totalChanges: number
    addedItems: number
    removedItems: number
    modifiedItems: number
    configChanges: number
  }
}

// 模板变更
export interface TemplateChange {
  type: 'modified'
  field: string
  oldValue: any
  newValue: any
  description: string
}

// 检查项变更
export interface ItemChange {
  type: 'added' | 'removed' | 'modified'
  itemId?: string
  sequence?: number
  field?: string
  oldValue?: any
  newValue?: any
  description: string
}

// 配置变更
export interface ConfigChange {
  type: 'added' | 'removed' | 'modified'
  category: 'buttonGroup' | 'defectRule' | 'tableConfig' | 'customField'
  id?: string
  field?: string
  oldValue?: any
  newValue?: any
  description: string
}

// 版本升级选项
export interface VersionUpgradeOptions {
  reviewId: string
  targetSnapshotId: string
  
  // 升级策略
  strategy: {
    // 检查项处理
    items: {
      addNewItems: boolean  // 是否添加新增的检查项
      removeDeletedItems: boolean  // 是否移除已删除的检查项
      updateModifiedItems: boolean  // 是否更新已修改的检查项
      preserveUserData: boolean  // 是否保留用户已填写的数据
    }
    
    // 配置处理
    config: {
      updateButtonGroups: boolean
      updateDefectRules: boolean
      updateTableConfigs: boolean
      updateCustomFields: boolean
    }
    
    // 冲突处理
    conflicts: {
      onItemConflict: 'keep-user' | 'use-template' | 'merge'
      onConfigConflict: 'keep-user' | 'use-template' | 'ask-user'
    }
  }
  
  // 备份选项
  backup: {
    createBackup: boolean
    backupDescription?: string
  }
}

// 版本升级结果
export interface VersionUpgradeResult {
  success: boolean
  reviewId: string
  oldSnapshotId: string
  newSnapshotId: string
  
  changes: {
    itemsAdded: number
    itemsRemoved: number
    itemsModified: number
    configsUpdated: number
  }
  
  conflicts: Array<{
    type: 'item' | 'config'
    id: string
    description: string
    resolution: string
  }>
  
  backup?: {
    backupId: string
    createdTime: string
  }
  
  errors: string[]
  warnings: string[]
}

// 模板版本管理器配置
export interface TemplateVersionConfig {
  // 自动创建快照的触发条件
  autoSnapshot: {
    enabled: boolean
    onTemplateChange: boolean  // 模板内容变更时
    onConfigChange: boolean   // 配置变更时
    onPublish: boolean        // 发布时
    minInterval: number       // 最小间隔（分钟）
  }
  
  // 版本保留策略
  retention: {
    maxVersions: number       // 最大保留版本数
    maxAge: number           // 最大保留时间（天）
    keepMajorVersions: boolean // 是否永久保留主版本
  }
  
  // 版本号策略
  versioning: {
    scheme: 'semantic' | 'timestamp' | 'sequential'  // 版本号方案
    autoIncrement: boolean    // 是否自动递增
    prefix?: string          // 版本号前缀
  }
}

// 工具函数：生成模板快照
export function createTemplateSnapshot(
  template: ChecklistTemplate,
  items: ChecklistItem[],
  config: {
    buttonGroups: StatusButtonGroup[]
    defectRules: DefectRule[]
    tableConfigs: TableViewConfig[]
    customFields: CustomFieldDefinition[]
  },
  version: string,
  createdBy: string,
  description?: string
): TemplateSnapshot {
  // 计算内容哈希
  const contentForHash = {
    template: {
      name: template.name,
      description: template.description,
      category: template.category
    },
    items: items.map(item => ({
      sequence: item.sequence,
      content: item.content,
      required: item.required,
      category: item.category
    })),
    config
  }
  
  const hash = generateHash(JSON.stringify(contentForHash))
  
  return {
    id: `snapshot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    templateId: template.id,
    version,
    createdTime: new Date().toISOString(),
    createdBy,
    description,
    
    template: {
      id: template.id,
      name: template.name,
      type: template.type,
      description: template.description,
      category: template.category,
      tags: template.tags || [],
      isActive: template.isActive
    },
    
    items: items.map(item => ({ ...item })), // 深拷贝
    
    config: {
      buttonGroups: config.buttonGroups.map(group => ({ ...group })),
      defectRules: config.defectRules.map(rule => ({ ...rule })),
      tableConfigs: config.tableConfigs.map(config => ({ ...config })),
      customFields: config.customFields.map(field => ({ ...field }))
    },
    
    metadata: {
      itemCount: items.length,
      requiredItemCount: items.filter(item => item.required).length,
      categoryCount: new Set(items.map(item => item.category)).size,
      buttonGroupCount: config.buttonGroups.length,
      defectRuleCount: config.defectRules.length,
      hash
    }
  }
}

// 工具函数：比较两个版本
export function compareVersions(
  currentSnapshot: TemplateSnapshot,
  targetSnapshot: TemplateSnapshot
): VersionComparison {
  const changes: VersionComparison['changes'] = {
    template: [],
    items: [],
    config: []
  }
  
  // 比较模板基本信息
  const templateFields = ['name', 'description', 'category', 'type']
  templateFields.forEach(field => {
    const oldValue = (currentSnapshot.template as any)[field]
    const newValue = (targetSnapshot.template as any)[field]
    if (oldValue !== newValue) {
      changes.template.push({
        type: 'modified',
        field,
        oldValue,
        newValue,
        description: `模板${field}从"${oldValue}"变更为"${newValue}"`
      })
    }
  })
  
  // 比较检查项
  const currentItems = new Map(currentSnapshot.items.map(item => [item.id, item]))
  const targetItems = new Map(targetSnapshot.items.map(item => [item.id, item]))
  
  // 检查新增和修改的项
  targetItems.forEach((targetItem, itemId) => {
    const currentItem = currentItems.get(itemId)
    if (!currentItem) {
      // 新增项
      changes.items.push({
        type: 'added',
        itemId,
        sequence: targetItem.sequence,
        description: `新增检查项: ${targetItem.content}`
      })
    } else {
      // 检查修改
      const itemFields = ['content', 'required', 'category', 'sequence']
      itemFields.forEach(field => {
        const oldValue = (currentItem as any)[field]
        const newValue = (targetItem as any)[field]
        if (oldValue !== newValue) {
          changes.items.push({
            type: 'modified',
            itemId,
            field,
            oldValue,
            newValue,
            description: `检查项"${currentItem.content}"的${field}从"${oldValue}"变更为"${newValue}"`
          })
        }
      })
    }
  })
  
  // 检查删除的项
  currentItems.forEach((currentItem, itemId) => {
    if (!targetItems.has(itemId)) {
      changes.items.push({
        type: 'removed',
        itemId,
        description: `删除检查项: ${currentItem.content}`
      })
    }
  })
  
  // 计算统计信息
  const summary = {
    totalChanges: changes.template.length + changes.items.length + changes.config.length,
    addedItems: changes.items.filter(c => c.type === 'added').length,
    removedItems: changes.items.filter(c => c.type === 'removed').length,
    modifiedItems: changes.items.filter(c => c.type === 'modified').length,
    configChanges: changes.config.length
  }
  
  return {
    templateId: currentSnapshot.templateId,
    currentVersion: currentSnapshot.version,
    targetVersion: targetSnapshot.version,
    changes,
    summary
  }
}

// 工具函数：生成简单哈希
function generateHash(content: string): string {
  let hash = 0
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  return Math.abs(hash).toString(16)
}

// 工具函数：生成版本号
export function generateVersion(
  scheme: 'semantic' | 'timestamp' | 'sequential',
  currentVersion?: string,
  changeType: 'major' | 'minor' | 'patch' = 'minor'
): string {
  switch (scheme) {
    case 'semantic':
      if (!currentVersion) return '1.0.0'
      const [major, minor, patch] = currentVersion.split('.').map(Number)
      switch (changeType) {
        case 'major': return `${major + 1}.0.0`
        case 'minor': return `${major}.${minor + 1}.0`
        case 'patch': return `${major}.${minor}.${patch + 1}`
      }
      break
      
    case 'timestamp':
      return new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)
      
    case 'sequential':
      if (!currentVersion) return '1'
      return String(parseInt(currentVersion) + 1)
      
    default:
      return '1.0.0'
  }
}
