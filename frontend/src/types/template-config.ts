import { type ChecklistTemplate, type ChecklistItem } from '@/api/template'
import { type TableViewConfig } from '@/types/table-config'
import { type DefectRule } from '@/types/defect-config'
import { type StatusButtonGroup } from '@/types/defect-config'

/**
 * 统一的模板配置数据结构
 * 包含模板的所有配置信息：基本信息、表头配置、检查项、缺陷规则、状态按钮
 */
export interface TemplateConfiguration {
  // 基本信息
  basicInfo: TemplateBasicInfo
  
  // 表头配置
  tableConfig: TableViewConfig | null
  
  // 检查项配置（支持动态字段）
  items: ExtendedChecklistItem[]
  
  // 缺陷生成规则
  defectRules: DefectRule[]
  
  // 状态按钮配置
  statusButtons: StatusButtonGroup[]
}

/**
 * 模板基本信息
 */
export interface TemplateBasicInfo {
  id?: string
  name: string
  type: string
  version?: string
  description?: string
  category?: string
  tags?: string[]
  isActive?: boolean
  createdTime?: any
  updatedTime?: any
}

/**
 * 扩展的检查项，支持动态字段
 */
export interface ExtendedChecklistItem extends ChecklistItem {
  // 动态字段，基于表头配置定义
  dynamicFields?: Record<string, any>
  
  // 临时ID，用于前端操作
  tempId?: string
}

/**
 * 模板配置保存请求
 */
export interface SaveTemplateConfigRequest {
  basicInfo: TemplateBasicInfo
  tableConfig?: TableViewConfig
  items: ExtendedChecklistItem[]
  defectRules?: DefectRule[]
  statusButtons?: StatusButtonGroup[]
}

/**
 * 模板配置加载响应
 */
export interface LoadTemplateConfigResponse {
  template: ChecklistTemplate
  tableConfig?: TableViewConfig
  defectRules: DefectRule[]
  statusButtons: StatusButtonGroup[]
}

/**
 * 标签页状态管理
 */
export interface TabState {
  activeTab: string
  completedTabs: Set<string>
  hasUnsavedChanges: boolean
}

/**
 * 表单验证状态
 */
export interface ValidationState {
  basicInfo: boolean
  tableConfig: boolean
  items: boolean
  defectRules: boolean
  statusButtons: boolean
}

/**
 * 默认的模板配置
 */
export const DEFAULT_TEMPLATE_CONFIG: TemplateConfiguration = {
  basicInfo: {
    name: '',
    type: '',
    description: '',
    category: '',
    tags: [],
    isActive: true
  },
  tableConfig: null,
  items: [],
  defectRules: [],
  statusButtons: []
}

/**
 * 标签页配置
 */
export interface TabConfig {
  key: string
  label: string
  icon?: string
  required: boolean
  order: number
}

export const TAB_CONFIGS: TabConfig[] = [
  {
    key: 'basicInfo',
    label: '基本信息',
    icon: 'InfoFilled',
    required: true,
    order: 1
  },
  {
    key: 'tableConfig',
    label: '表头配置',
    icon: 'Grid',
    required: false,
    order: 2
  },
  {
    key: 'items',
    label: '检查项配置',
    icon: 'List',
    required: true,
    order: 3
  },
  {
    key: 'defectRules',
    label: '缺陷规则',
    icon: 'Warning',
    required: false,
    order: 4
  },
  {
    key: 'statusButtons',
    label: '状态按钮',
    icon: 'Operation',
    required: false,
    order: 5
  }
]

/**
 * 工具函数：从现有模板创建配置
 */
export function createConfigFromTemplate(template: ChecklistTemplate): TemplateConfiguration {
  return {
    basicInfo: {
      id: template.id,
      name: template.name,
      type: template.type,
      version: template.version,
      description: template.description,
      category: template.category,
      tags: template.tags,
      isActive: template.isActive,
      createdTime: template.createdTime,
      updatedTime: template.updatedTime
    },
    tableConfig: null, // 需要单独加载
    items: template.items.map((item, index) => ({
      ...item,
      tempId: `item_${Date.now()}_${index}`,
      dynamicFields: {}
    })),
    defectRules: [], // 需要单独加载
    statusButtons: [] // 需要单独加载
  }
}

/**
 * 工具函数：验证配置完整性
 */
export function validateTemplateConfig(config: TemplateConfiguration): ValidationState {
  return {
    basicInfo: !!(config.basicInfo.name && config.basicInfo.type),
    tableConfig: !!config.tableConfig, // 表头配置是必填的
    items: config.items.length > 0,
    defectRules: config.defectRules.length > 0, // 缺陷规则是必填的
    statusButtons: config.statusButtons.length > 0 // 状态按钮是必填的
  }
}

/**
 * 工具函数：检查是否有未保存的更改
 */
export function hasUnsavedChanges(
  current: TemplateConfiguration,
  original: TemplateConfiguration
): boolean {
  return JSON.stringify(current) !== JSON.stringify(original)
}
