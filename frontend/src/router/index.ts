import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/review',
    },
    {
      path: '/admin',
      redirect: '/admin/templates',
    },
    {
      path: '/admin/templates',
      name: 'ChecklistContentEditor',
      component: () => import('@/views/admin/ChecklistContentEditor.vue'),
      meta: {
        title: 'CheckList评审表内容编辑',
        requiresAuth: false
      },
    },
    {
      path: '/admin/config',
      name: 'ChecklistItemConfig',
      component: () => import('@/views/admin/ChecklistItemConfig.vue'),
      meta: {
        title: 'CheckList评审项配置',
        requiresAuth: false
      },
    },
    {
      path: '/admin/settings',
      name: 'ChecklistSettings',
      component: () => import('@/views/admin/ChecklistSettings.vue'),
      meta: {
        title: 'CheckList配置',
        requiresAuth: false
      },
    },
    {
      path: '/review',
      name: 'ReviewDashboard',
      component: () => import('@/views/ReviewDashboard.vue'),
      meta: { 
        title: '评审系统',
        breadcrumb: [
          { text: '首页', to: '/' },
          { text: '评审系统', to: '/review' }
        ]
      },
    },
    {
      path: '/review/:type',
      name: 'ChecklistReview',
      component: () => import('@/views/ChecklistReview.vue'),
      meta: { 
        title: '检查单评审',
        breadcrumb: [
          { text: '首页', to: '/' },
          { text: '评审系统', to: '/review' },
          { text: '检查单评审', to: '' }
        ]
      },
    },
    {
      path: '/review/:type/history',
      name: 'ReviewHistory',
      component: () => import('@/components/review/ReviewHistory.vue'),
      meta: {
        title: '评审历史',
        breadcrumb: [
          { text: '首页', to: '/' },
          { text: '评审系统', to: '/review' },
          { text: '评审历史', to: '' }
        ]
      }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      redirect: '/review'
    }
  ],
})

// Navigation guard for setting page title and breadcrumbs
router.beforeEach((to, from, next) => {
  // Set page title
  if (to.meta?.title) {
    document.title = `${to.meta.title} - Checklist Review System`
  }
  
  // Future: Add authentication check here
  // if (to.meta?.requiresAuth && !isAuthenticated()) {
  //   next('/login')
  //   return
  // }
  
  next()
})

// After navigation hook for additional setup
router.afterEach((to, from) => {
  // Future: Add analytics tracking here
  // trackPageView(to.path)
})

export default router
