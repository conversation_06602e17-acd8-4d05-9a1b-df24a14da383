<template>
  <div class="checklist-settings">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">🔧</div>
          <div class="header-text">
            <h1>CheckList系统配置</h1>
            <p>系统设置、数据导入和配置管理</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="handleGoToTemplates">
            <el-icon><Document /></el-icon>
            查看模板
          </el-button>
        </div>
      </div>
    </div>

    <div class="settings-tabs">
      <el-tabs v-model="activeTab" class="custom-tabs">
        <el-tab-pane label="📥 数据导入" name="import">
          <div class="tab-content">
            <ExcelImport @go-to-templates="handleGoToTemplates" />
          </div>
        </el-tab-pane>

        <el-tab-pane label="⚙️ 系统设置" name="system">
          <div class="tab-content">
            <SystemConfig />
          </div>
        </el-tab-pane>

        <el-tab-pane label="👥 用户管理" name="users">
          <div class="tab-content">
            <UserManagement />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Document } from '@element-plus/icons-vue'
import ExcelImport from '@/components/admin/ExcelImport.vue'

const router = useRouter()
const activeTab = ref('import')

// 创建占位组件
const SystemConfig = {
  template: `
    <div class="placeholder-content">
      <div class="placeholder-icon">⚙️</div>
      <h3>系统设置</h3>
      <p>系统参数配置功能开发中...</p>
    </div>
  `
}

const UserManagement = {
  template: `
    <div class="placeholder-content">
      <div class="placeholder-icon">👥</div>
      <h3>用户管理</h3>
      <p>用户权限管理功能开发中...</p>
    </div>
  `
}

const handleGoToTemplates = () => {
  // 跳转到模板编辑页面
  router.push('/admin/templates')
}

// Set page title
onMounted(() => {
  document.title = 'CheckList系统配置 - Checklist Review System'
})
</script>

<style scoped>
.checklist-settings {
  min-height: 100vh;
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  padding: 24px;
}

.page-header {
  background: #fff;
  margin-bottom: 24px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.header-content {
  padding: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-icon {
  font-size: 48px;
}

.header-text h1 {
  color: #303133;
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-text p {
  color: #606266;
  margin: 0;
  font-size: 16px;
  line-height: 1.6;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.settings-tabs {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.custom-tabs {
  padding: 0;
}

.tab-content {
  padding: 32px;
  min-height: 400px;
}

.placeholder-content {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.placeholder-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.placeholder-content h3 {
  font-size: 20px;
  color: #606266;
  margin: 0 0 12px 0;
}

.placeholder-content p {
  font-size: 14px;
  color: #909399;
  margin: 0;
}
</style>
