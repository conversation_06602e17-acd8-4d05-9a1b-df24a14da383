<template>
  <div class="defect-manager">
    <!-- 缺陷列表头部 -->
    <div class="defect-header">
      <div class="header-title">
        <h3>生成的缺陷</h3>
        <el-badge :value="defects.length" type="primary" />
      </div>
      
      <div class="header-actions">
        <el-button-group>
          <el-button
            @click="batchConfirmDefects"
            :disabled="selectedDefects.length === 0"
            type="primary"
            size="small"
          >
            <el-icon><Check /></el-icon>
            批量确认 ({{ selectedDefects.length }})
          </el-button>
          
          <el-button
            @click="exportDefects"
            :disabled="defects.length === 0"
            size="small"
          >
            <el-icon><Download /></el-icon>
            导出缺陷
          </el-button>
          
          <el-button
            @click="clearAllDefects"
            :disabled="defects.length === 0"
            type="danger"
            size="small"
          >
            <el-icon><Delete /></el-icon>
            清空
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 缺陷统计 -->
    <div v-if="defects.length > 0" class="defect-stats">
      <div class="stat-item">
        <span class="stat-label">总计:</span>
        <span class="stat-value">{{ defects.length }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">草稿:</span>
        <span class="stat-value draft">{{ draftCount }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">已确认:</span>
        <span class="stat-value confirmed">{{ confirmedCount }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">已提交:</span>
        <span class="stat-value submitted">{{ submittedCount }}</span>
      </div>
    </div>

    <!-- 缺陷列表 -->
    <div class="defect-list">
      <div v-if="defects.length === 0" class="empty-state">
        <div class="empty-icon">📋</div>
        <div class="empty-text">暂无生成的缺陷</div>
        <div class="empty-hint">当检查项标记为不通过时，会自动生成对应缺陷</div>
      </div>
      
      <div v-else class="defect-items">
        <div
          v-for="defect in defects"
          :key="defect.id"
          :class="['defect-item', `status-${defect.status}`]"
        >
          <!-- 缺陷头部 -->
          <div class="defect-item-header">
            <div class="defect-selection">
              <el-checkbox
                :model-value="selectedDefects.includes(defect.id)"
                @update:model-value="(checked: boolean) => handleDefectSelect(defect.id, checked)"
              />
            </div>
            
            <div class="defect-title">
              <h4>{{ defect.title }}</h4>
              <div class="defect-meta">
                <el-tag :type="getSeverityTagType(defect.severity)" size="small">
                  {{ getSeverityText(defect.severity) }}
                </el-tag>
                <el-tag type="info" size="small">{{ defect.category }}</el-tag>
                <el-tag :type="getStatusTagType(defect.status)" size="small">
                  {{ getStatusText(defect.status) }}
                </el-tag>
                <span class="defect-time">{{ formatTime(defect.createdTime) }}</span>
              </div>
            </div>
            
            <div class="defect-actions">
              <el-button-group>
                <el-button
                  v-if="defect.status === 'draft'"
                  @click="confirmDefect(defect)"
                  type="primary"
                  size="small"
                >
                  确认
                </el-button>
                
                <el-button
                  v-if="defect.status === 'confirmed'"
                  @click="submitDefect(defect)"
                  type="success"
                  size="small"
                >
                  提交
                </el-button>
                
                <el-button
                  @click="editDefect(defect)"
                  size="small"
                >
                  <el-icon><Edit /></el-icon>
                </el-button>
                
                <el-button
                  @click="deleteDefect(defect.id)"
                  type="danger"
                  size="small"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-button-group>
            </div>
          </div>

          <!-- 缺陷内容 -->
          <div class="defect-content">
            <div class="defect-description">
              <p>{{ defect.description }}</p>
            </div>
            
            <div class="defect-source">
              <el-descriptions :column="2" size="small" border>
                <el-descriptions-item label="源检查项">
                  {{ defect.sourceContent }}
                </el-descriptions-item>
                <el-descriptions-item label="检查项ID">
                  {{ defect.sourceItemId }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
            
            <!-- 自定义数据 -->
            <div v-if="Object.keys(defect.customData).length > 0" class="defect-custom-data">
              <h5>附加信息</h5>
              <el-descriptions :column="2" size="small">
                <el-descriptions-item
                  v-for="[key, value] in Object.entries(defect.customData)"
                  :key="key"
                  :label="key"
                >
                  {{ value }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑缺陷对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑缺陷"
      width="700px"
    >
      <div v-if="editingDefect" class="edit-defect-form">
        <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="100px">
          <el-form-item label="缺陷标题" prop="title">
            <el-input v-model="editForm.title" />
          </el-form-item>
          
          <el-form-item label="缺陷描述" prop="description">
            <el-input
              v-model="editForm.description"
              type="textarea"
              :rows="4"
            />
          </el-form-item>
          
          <el-form-item label="严重程度" prop="severity">
            <el-select v-model="editForm.severity">
              <el-option label="高" value="high" />
              <el-option label="中" value="medium" />
              <el-option label="低" value="low" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="缺陷分类" prop="category">
            <el-input v-model="editForm.category" />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelEdit">取消</el-button>
          <el-button type="primary" @click="saveEdit">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Check, Download, Delete, Edit } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { type GeneratedDefect } from '@/types/defect-config'

// Props
interface Props {
  defects: GeneratedDefect[]
}

const props = withDefaults(defineProps<Props>(), {
  defects: () => []
})

// Emits
interface Emits {
  (e: 'defect-confirm', defect: GeneratedDefect): void
  (e: 'defect-submit', defect: GeneratedDefect): void
  (e: 'defect-edit', defect: GeneratedDefect): void
  (e: 'defect-delete', defectId: string): void
  (e: 'defects-export', defects: GeneratedDefect[]): void
  (e: 'defects-clear'): void
}

const emit = defineEmits<Emits>()

// Reactive data
const selectedDefects = ref<string[]>([])
const showEditDialog = ref(false)
const editingDefect = ref<GeneratedDefect | null>(null)
const editForm = ref({
  title: '',
  description: '',
  severity: 'medium',
  category: ''
})
const editFormRef = ref()

// Validation rules
const editRules = {
  title: [{ required: true, message: '请输入缺陷标题', trigger: 'blur' }],
  description: [{ required: true, message: '请输入缺陷描述', trigger: 'blur' }],
  severity: [{ required: true, message: '请选择严重程度', trigger: 'change' }],
  category: [{ required: true, message: '请输入缺陷分类', trigger: 'blur' }]
}

// Computed
const draftCount = computed(() => 
  props.defects.filter(d => d.status === 'draft').length
)

const confirmedCount = computed(() => 
  props.defects.filter(d => d.status === 'confirmed').length
)

const submittedCount = computed(() => 
  props.defects.filter(d => d.status === 'submitted').length
)

// Methods
const handleDefectSelect = (defectId: string, checked: boolean) => {
  if (checked) {
    selectedDefects.value.push(defectId)
  } else {
    const index = selectedDefects.value.indexOf(defectId)
    if (index > -1) {
      selectedDefects.value.splice(index, 1)
    }
  }
}

const batchConfirmDefects = async () => {
  const defectsToConfirm = props.defects.filter(d => 
    selectedDefects.value.includes(d.id) && d.status === 'draft'
  )
  
  if (defectsToConfirm.length === 0) {
    ElMessage.warning('没有可确认的缺陷')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确认批量确认 ${defectsToConfirm.length} 个缺陷？`,
      '批量确认',
      { type: 'warning' }
    )
    
    defectsToConfirm.forEach(defect => {
      defect.status = 'confirmed'
      emit('defect-confirm', defect)
    })
    
    selectedDefects.value = []
    ElMessage.success(`已确认 ${defectsToConfirm.length} 个缺陷`)
  } catch {
    // User cancelled
  }
}

const confirmDefect = (defect: GeneratedDefect) => {
  defect.status = 'confirmed'
  emit('defect-confirm', defect)
  ElMessage.success('缺陷已确认')
}

const submitDefect = (defect: GeneratedDefect) => {
  defect.status = 'submitted'
  emit('defect-submit', defect)
  ElMessage.success('缺陷已提交')
}

const editDefect = (defect: GeneratedDefect) => {
  editingDefect.value = defect
  editForm.value = {
    title: defect.title,
    description: defect.description,
    severity: defect.severity,
    category: defect.category
  }
  showEditDialog.value = true
}

const saveEdit = async () => {
  try {
    await editFormRef.value.validate()
    
    if (editingDefect.value) {
      Object.assign(editingDefect.value, editForm.value)
      emit('defect-edit', editingDefect.value)
    }
    
    showEditDialog.value = false
    ElMessage.success('缺陷已更新')
  } catch {
    // Validation failed
  }
}

const cancelEdit = () => {
  showEditDialog.value = false
  editingDefect.value = null
}

const deleteDefect = async (defectId: string) => {
  try {
    await ElMessageBox.confirm('确认删除此缺陷？', '确认删除', {
      type: 'warning'
    })
    
    emit('defect-delete', defectId)
    
    // Remove from selection if selected
    const index = selectedDefects.value.indexOf(defectId)
    if (index > -1) {
      selectedDefects.value.splice(index, 1)
    }
    
    ElMessage.success('缺陷已删除')
  } catch {
    // User cancelled
  }
}

const exportDefects = () => {
  emit('defects-export', props.defects)
}

const clearAllDefects = async () => {
  try {
    await ElMessageBox.confirm('确认清空所有缺陷？', '确认清空', {
      type: 'warning'
    })
    
    emit('defects-clear')
    selectedDefects.value = []
    ElMessage.success('已清空所有缺陷')
  } catch {
    // User cancelled
  }
}

// Utility methods
const getSeverityTagType = (severity: string) => {
  const typeMap: Record<string, string> = {
    high: 'danger',
    medium: 'warning',
    low: 'info'
  }
  return typeMap[severity] || 'info'
}

const getSeverityText = (severity: string) => {
  const textMap: Record<string, string> = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return textMap[severity] || severity
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    draft: 'info',
    confirmed: 'warning',
    submitted: 'success'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    draft: '草稿',
    confirmed: '已确认',
    submitted: '已提交'
  }
  return textMap[status] || status
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.defect-manager {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.defect-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title h3 {
  margin: 0;
  color: #303133;
}

.defect-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

.stat-value {
  font-weight: 500;
  color: #303133;
}

.stat-value.draft {
  color: #909399;
}

.stat-value.confirmed {
  color: #e6a23c;
}

.stat-value.submitted {
  color: #67c23a;
}

.defect-list {
  .empty-state {
    text-align: center;
    padding: 40px;
    color: #909399;
  }

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 16px;
    margin-bottom: 8px;
  }

  .empty-hint {
    font-size: 14px;
    color: #c0c4cc;
  }
}

.defect-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
  transition: all 0.3s;
}

.defect-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.defect-item.status-draft {
  border-left: 4px solid #909399;
}

.defect-item.status-confirmed {
  border-left: 4px solid #e6a23c;
}

.defect-item.status-submitted {
  border-left: 4px solid #67c23a;
}

.defect-item-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #fafbfc;
  border-bottom: 1px solid #e4e7ed;
}

.defect-selection {
  margin-top: 4px;
}

.defect-title {
  flex: 1;
}

.defect-title h4 {
  margin: 0 0 8px 0;
  color: #303133;
  line-height: 1.4;
}

.defect-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.defect-time {
  color: #909399;
  font-size: 12px;
}

.defect-content {
  padding: 16px;
}

.defect-description {
  margin-bottom: 16px;
}

.defect-description p {
  margin: 0;
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
}

.defect-source {
  margin-bottom: 16px;
}

.defect-custom-data h5 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .defect-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .defect-stats {
    flex-direction: column;
    gap: 8px;
  }

  .defect-item-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .defect-meta {
    justify-content: flex-start;
  }
}
</style>
