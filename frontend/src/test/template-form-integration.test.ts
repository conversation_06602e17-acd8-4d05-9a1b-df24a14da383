/**
 * 模板表单集成测试
 * 验证模板编辑界面的功能完整性和数据一致性
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElMessage } from 'element-plus'
import TemplateForm from '@/components/admin/TemplateForm.vue'
import {
  type TemplateConfiguration,
  DEFAULT_TEMPLATE_CONFIG,
  validateTemplateConfig,
  hasUnsavedChanges,
} from '@/types/template-config'
import { type ChecklistTemplate } from '@/api/template'

// Mock ElMessage
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn(),
  },
  ElMessageBox: {
    confirm: vi.fn(),
    prompt: vi.fn(),
  },
}))

// Mock API calls
vi.mock('@/api/template-config', () => ({
  saveTemplateConfig: vi.fn(),
  loadTemplateConfig: vi.fn(),
}))

vi.mock('@/api/template', () => ({
  createTemplate: vi.fn(),
  updateTemplate: vi.fn(),
  getTemplateTypes: vi.fn(() => Promise.resolve(['类型1', '类型2'])),
}))

describe('TemplateForm Integration Tests', () => {
  let wrapper: any
  let mockTemplate: ChecklistTemplate

  beforeEach(() => {
    mockTemplate = {
      id: 'test-template-1',
      name: '测试模板',
      type: '测试类型',
      version: '1.0.0',
      description: '这是一个测试模板',
      category: '测试分类',
      tags: ['测试', '集成'],
      isActive: true,
      items: [
        {
          id: 'item-1',
          sequence: 1,
          content: '测试检查项1',
          category: '基础检查',
          required: true,
        },
        {
          id: 'item-2',
          sequence: 2,
          content: '测试检查项2',
          category: '高级检查',
          required: false,
        },
      ],
      createdTime: new Date(),
      updatedTime: new Date(),
    }
  })

  describe('数据结构验证', () => {
    it('应该正确验证默认配置', () => {
      const validation = validateTemplateConfig(DEFAULT_TEMPLATE_CONFIG)
      
      expect(validation.basicInfo).toBe(false) // 缺少必填字段
      expect(validation.tableConfig).toBe(true) // 可选
      expect(validation.items).toBe(false) // 没有检查项
      expect(validation.defectRules).toBe(true) // 可选
      expect(validation.statusButtons).toBe(true) // 可选
    })

    it('应该正确验证完整配置', () => {
      const completeConfig: TemplateConfiguration = {
        basicInfo: {
          name: '完整模板',
          type: '测试类型',
          description: '完整的测试模板',
          category: '测试',
          tags: ['完整'],
          isActive: true,
        },
        tableConfig: null,
        items: [
          {
            id: 'item-1',
            sequence: 1,
            content: '测试项',
            category: '测试',
            required: true,
            tempId: 'temp-1',
            dynamicFields: {},
          },
        ],
        defectRules: [],
        statusButtons: [],
      }

      const validation = validateTemplateConfig(completeConfig)
      
      expect(validation.basicInfo).toBe(true)
      expect(validation.items).toBe(true)
    })

    it('应该正确检测未保存的更改', () => {
      const original = { ...DEFAULT_TEMPLATE_CONFIG }
      const modified = {
        ...DEFAULT_TEMPLATE_CONFIG,
        basicInfo: {
          ...DEFAULT_TEMPLATE_CONFIG.basicInfo,
          name: '修改后的名称',
        },
      }

      expect(hasUnsavedChanges(original, original)).toBe(false)
      expect(hasUnsavedChanges(modified, original)).toBe(true)
    })
  })

  describe('标签页功能', () => {
    beforeEach(() => {
      wrapper = mount(TemplateForm, {
        props: {
          visible: true,
          mode: 'edit',
          template: mockTemplate,
        },
        global: {
          stubs: {
            'el-dialog': true,
            'el-tabs': true,
            'el-tab-pane': true,
            'el-form': true,
            'el-form-item': true,
            'el-input': true,
            'el-select': true,
            'el-switch': true,
            'el-button': true,
            'el-tag': true,
            'el-empty': true,
            'el-icon': true,
            'TableColumnConfig': true,
          },
        },
      })
    })

    it('应该包含所有必需的标签页', () => {
      const expectedTabs = [
        'basicInfo',
        'tableConfig', 
        'items',
        'defectRules',
        'statusButtons',
      ]

      // 验证组件数据中包含所有标签页
      expect(wrapper.vm.activeTab).toBeDefined()
      
      // 这里可以添加更多具体的标签页验证逻辑
    })

    it('应该正确初始化模板数据', async () => {
      await wrapper.vm.$nextTick()
      
      // 验证基本信息是否正确加载
      expect(wrapper.vm.templateConfig.basicInfo.name).toBe(mockTemplate.name)
      expect(wrapper.vm.templateConfig.basicInfo.type).toBe(mockTemplate.type)
      expect(wrapper.vm.templateConfig.items.length).toBe(mockTemplate.items.length)
    })
  })

  describe('数据保存功能', () => {
    it('应该在保存前验证所有必填字段', async () => {
      wrapper = mount(TemplateForm, {
        props: {
          visible: true,
          mode: 'create',
        },
        global: {
          stubs: {
            'el-dialog': true,
            'el-form': true,
            'el-form-item': true,
            'el-input': true,
            'el-button': true,
          },
        },
      })

      // 尝试保存空表单
      await wrapper.vm.handleSave()
      
      // 应该显示验证错误
      expect(ElMessage.error).toHaveBeenCalled()
    })

    it('应该正确处理编辑模式的数据保存', async () => {
      const { saveTemplateConfig } = await import('@/api/template-config')
      
      wrapper = mount(TemplateForm, {
        props: {
          visible: true,
          mode: 'edit',
          template: mockTemplate,
        },
        global: {
          stubs: {
            'el-dialog': true,
            'el-form': true,
            'el-form-item': true,
            'el-input': true,
            'el-button': true,
          },
        },
      })

      // 模拟表单验证通过
      wrapper.vm.basicInfoFormRef = {
        validate: vi.fn(() => Promise.resolve(true)),
      }

      await wrapper.vm.handleSave()
      
      // 验证API调用
      expect(saveTemplateConfig).toHaveBeenCalledWith(
        mockTemplate.id,
        expect.objectContaining({
          basicInfo: expect.any(Object),
          items: expect.any(Array),
        })
      )
    })
  })

  describe('动态字段集成', () => {
    it('应该根据表头配置生成动态字段', () => {
      const tableConfig = {
        id: 'test-config',
        name: '测试配置',
        columns: [
          {
            id: 'col-1',
            key: 'priority',
            label: '优先级',
            type: 'tag',
            visible: true,
            fixed: false,
            options: ['高', '中', '低'],
          },
          {
            id: 'col-2',
            key: 'deadline',
            label: '截止日期',
            type: 'date',
            visible: true,
            fixed: false,
          },
        ],
      }

      wrapper = mount(TemplateForm, {
        props: {
          visible: true,
          mode: 'create',
        },
        global: {
          stubs: {
            'el-dialog': true,
            'el-form': true,
            'el-form-item': true,
            'el-input': true,
            'el-select': true,
            'el-date-picker': true,
          },
        },
      })

      // 设置表头配置
      wrapper.vm.templateConfig.tableConfig = tableConfig

      // 验证动态字段计算
      expect(wrapper.vm.customColumnsCount).toBe(2)
    })
  })
})

// 导出测试工具函数
export function createMockTemplate(overrides: Partial<ChecklistTemplate> = {}): ChecklistTemplate {
  return {
    id: 'mock-template',
    name: '模拟模板',
    type: '模拟类型',
    version: '1.0.0',
    description: '模拟描述',
    category: '模拟分类',
    tags: ['模拟'],
    isActive: true,
    items: [],
    createdTime: new Date(),
    updatedTime: new Date(),
    ...overrides,
  }
}

export function createMockTemplateConfig(overrides: Partial<TemplateConfiguration> = {}): TemplateConfiguration {
  return {
    ...DEFAULT_TEMPLATE_CONFIG,
    ...overrides,
  }
}
