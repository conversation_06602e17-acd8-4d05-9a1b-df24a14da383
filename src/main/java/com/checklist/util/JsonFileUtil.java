package com.checklist.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * JSON文件操作工具类
 */
@Component
public class JsonFileUtil {
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 保存对象到JSON文件
     */
    public <T> void saveToFile(String directory, String fileName, T object) throws IOException {
        // 确保目录存在
        Path dirPath = Paths.get(directory);
        if (!Files.exists(dirPath)) {
            Files.createDirectories(dirPath);
        }
        
        // 保存文件
        File file = new File(directory, fileName);
        objectMapper.writeValue(file, object);
    }
    
    /**
     * 从JSON文件加载对象
     */
    public <T> T loadFromFile(String directory, String fileName, Class<T> clazz) throws IOException {
        File file = new File(directory, fileName);
        if (!file.exists()) {
            return null;
        }
        
        return objectMapper.readValue(file, clazz);
    }
    
    /**
     * 从文件对象加载
     */
    public <T> T loadFromFile(File file, Class<T> clazz) throws IOException {
        if (!file.exists()) {
            return null;
        }
        
        return objectMapper.readValue(file, clazz);
    }
    
    /**
     * 删除文件
     */
    public boolean deleteFile(String directory, String fileName) {
        File file = new File(directory, fileName);
        return file.exists() && file.delete();
    }
    
    /**
     * 检查文件是否存在
     */
    public boolean fileExists(String directory, String fileName) {
        File file = new File(directory, fileName);
        return file.exists();
    }
    
    /**
     * 获取目录下所有JSON文件
     */
    public File[] getJsonFiles(String directory) {
        File dir = new File(directory);
        if (!dir.exists() || !dir.isDirectory()) {
            return new File[0];
        }
        
        return dir.listFiles((dir1, name) -> name.endsWith(".json"));
    }
}
