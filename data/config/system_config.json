{"systemName": "CheckList Review System", "version": "1.0.0", "settings": {"maxFileSize": "10MB", "allowedFileTypes": ["json", "xlsx", "xls"], "autoSaveInterval": 30000, "defaultReviewMode": "single", "enableMultiReviewer": true, "maxReviewers": 5}, "reviewTypes": [{"id": "code-review", "name": "代码评审", "description": "代码质量和规范检查", "enabled": true}, {"id": "design-review", "name": "设计评审", "description": "系统设计和架构评审", "enabled": true}, {"id": "security-review", "name": "安全评审", "description": "安全漏洞和风险评估", "enabled": true}, {"id": "performance-review", "name": "性能评审", "description": "系统性能和优化评审", "enabled": false}], "categories": ["代码规范", "文档", "测试", "安全", "性能", "重构", "异常处理", "数据库", "架构设计", "接口设计", "UI设计", "可扩展性"]}