{"id": "template_demo_code_review", "name": "代码评审检查单", "type": "code-review", "version": "1.0", "createdTime": "2025-01-01 10:00:00", "updatedTime": "2025-01-01 10:00:00", "items": [{"id": "item_001", "sequence": 1, "content": "代码是否遵循团队编码规范？", "required": true, "category": "代码规范"}, {"id": "item_002", "sequence": 2, "content": "是否有适当的注释和文档？", "required": true, "category": "文档"}, {"id": "item_003", "sequence": 3, "content": "单元测试覆盖率是否达标？", "required": true, "category": "测试"}, {"id": "item_004", "sequence": 4, "content": "是否存在潜在的安全漏洞？", "required": true, "category": "安全"}, {"id": "item_005", "sequence": 5, "content": "代码性能是否满足要求？", "required": false, "category": "性能"}, {"id": "item_006", "sequence": 6, "content": "是否有重复代码需要重构？", "required": false, "category": "重构"}, {"id": "item_007", "sequence": 7, "content": "异常处理是否完善？", "required": true, "category": "异常处理"}, {"id": "item_008", "sequence": 8, "content": "数据库操作是否安全？", "required": true, "category": "数据库"}]}