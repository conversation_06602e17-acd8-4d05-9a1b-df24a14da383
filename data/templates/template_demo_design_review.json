{"id": "template_demo_design_review", "name": "设计评审检查单", "type": "design-review", "version": "1.0", "createdTime": "2025-01-01 10:00:00", "updatedTime": "2025-01-01 10:00:00", "items": [{"id": "item_d001", "sequence": 1, "content": "系统架构设计是否合理？", "required": true, "category": "架构设计"}, {"id": "item_d002", "sequence": 2, "content": "数据库设计是否符合规范？", "required": true, "category": "数据库设计"}, {"id": "item_d003", "sequence": 3, "content": "API接口设计是否完整？", "required": true, "category": "接口设计"}, {"id": "item_d004", "sequence": 4, "content": "用户界面设计是否友好？", "required": true, "category": "UI设计"}, {"id": "item_d005", "sequence": 5, "content": "系统可扩展性如何？", "required": false, "category": "可扩展性"}, {"id": "item_d006", "sequence": 6, "content": "安全设计是否充分？", "required": true, "category": "安全设计"}]}