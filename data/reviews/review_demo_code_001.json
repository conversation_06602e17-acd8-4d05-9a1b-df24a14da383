{"id": "review_demo_code_001", "templateId": "template_demo_code_review", "templateVersion": "1.0", "type": "code-review", "createdTime": "2025-01-01 14:30:00", "status": "IN_PROGRESS", "progress": {"total": 8, "completed": 5, "passed": 3, "failed": 1, "skipped": 1, "percentage": 62}, "reviewItems": [{"itemId": "item_001", "sequence": 1, "content": "代码是否遵循团队编码规范？", "category": "代码规范", "status": "PASS", "comment": "代码格式规范，命名清晰", "reviewHistory": [{"reviewer": "张三", "reviewTime": "2025-01-01 14:35:00", "status": "PASS", "comment": "代码格式规范，命名清晰"}]}, {"itemId": "item_002", "sequence": 2, "content": "是否有适当的注释和文档？", "category": "文档", "status": "FAIL", "comment": "缺少关键函数的注释", "reviewHistory": [{"reviewer": "张三", "reviewTime": "2025-01-01 14:36:00", "status": "FAIL", "comment": "缺少关键函数的注释"}]}, {"itemId": "item_003", "sequence": 3, "content": "单元测试覆盖率是否达标？", "category": "测试", "status": "PASS", "comment": "测试覆盖率达到85%", "reviewHistory": [{"reviewer": "张三", "reviewTime": "2025-01-01 14:37:00", "status": "PASS", "comment": "测试覆盖率达到85%"}]}, {"itemId": "item_004", "sequence": 4, "content": "是否存在潜在的安全漏洞？", "category": "安全", "status": "PASS", "comment": "未发现明显安全问题", "reviewHistory": [{"reviewer": "张三", "reviewTime": "2025-01-01 14:38:00", "status": "PASS", "comment": "未发现明显安全问题"}]}, {"itemId": "item_005", "sequence": 5, "content": "代码性能是否满足要求？", "category": "性能", "status": "SKIP", "comment": "暂时跳过，后续专项测试", "reviewHistory": [{"reviewer": "张三", "reviewTime": "2025-01-01 14:39:00", "status": "SKIP", "comment": "暂时跳过，后续专项测试"}]}, {"itemId": "item_006", "sequence": 6, "content": "是否有重复代码需要重构？", "category": "重构", "status": "PENDING", "comment": "", "reviewHistory": []}, {"itemId": "item_007", "sequence": 7, "content": "异常处理是否完善？", "category": "异常处理", "status": "PENDING", "comment": "", "reviewHistory": []}, {"itemId": "item_008", "sequence": 8, "content": "数据库操作是否安全？", "category": "数据库", "status": "PENDING", "comment": "", "reviewHistory": []}]}