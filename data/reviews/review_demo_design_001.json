{"id": "review_demo_design_001", "templateId": "template_demo_design_review", "templateVersion": "1.0", "type": "design-review", "createdTime": "2025-01-01 15:00:00", "status": "COMPLETED", "progress": {"total": 6, "completed": 6, "passed": 5, "failed": 1, "skipped": 0, "percentage": 100}, "reviewItems": [{"itemId": "item_d001", "sequence": 1, "content": "系统架构设计是否合理？", "category": "架构设计", "status": "PASS", "comment": "微服务架构设计合理", "reviewHistory": [{"reviewer": "李四", "reviewTime": "2025-01-01 15:05:00", "status": "PASS", "comment": "微服务架构设计合理"}]}, {"itemId": "item_d002", "sequence": 2, "content": "数据库设计是否符合规范？", "category": "数据库设计", "status": "PASS", "comment": "表结构设计规范，索引合理", "reviewHistory": [{"reviewer": "李四", "reviewTime": "2025-01-01 15:06:00", "status": "PASS", "comment": "表结构设计规范，索引合理"}]}, {"itemId": "item_d003", "sequence": 3, "content": "API接口设计是否完整？", "category": "接口设计", "status": "FAIL", "comment": "缺少错误处理的接口定义", "reviewHistory": [{"reviewer": "李四", "reviewTime": "2025-01-01 15:07:00", "status": "FAIL", "comment": "缺少错误处理的接口定义"}]}, {"itemId": "item_d004", "sequence": 4, "content": "用户界面设计是否友好？", "category": "UI设计", "status": "PASS", "comment": "界面简洁，用户体验良好", "reviewHistory": [{"reviewer": "李四", "reviewTime": "2025-01-01 15:08:00", "status": "PASS", "comment": "界面简洁，用户体验良好"}]}, {"itemId": "item_d005", "sequence": 5, "content": "系统可扩展性如何？", "category": "可扩展性", "status": "PASS", "comment": "采用插件化架构，扩展性好", "reviewHistory": [{"reviewer": "李四", "reviewTime": "2025-01-01 15:09:00", "status": "PASS", "comment": "采用插件化架构，扩展性好"}]}, {"itemId": "item_d006", "sequence": 6, "content": "安全设计是否充分？", "category": "安全设计", "status": "PASS", "comment": "安全机制完善", "reviewHistory": [{"reviewer": "李四", "reviewTime": "2025-01-01 15:10:00", "status": "PASS", "comment": "安全机制完善"}]}]}