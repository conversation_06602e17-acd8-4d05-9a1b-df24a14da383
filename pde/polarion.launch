<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<launchConfiguration type="org.eclipse.pde.ui.RuntimeWorkbench">
    <booleanAttribute key="append.args" value="true"/>
    <stringAttribute key="application" value="com.polarion.core.boot.app"/>
    <booleanAttribute key="askclear" value="true"/>
    <booleanAttribute key="automaticAdd" value="true"/>
    <booleanAttribute key="automaticValidate" value="true"/>
    <stringAttribute key="bootstrap" value=""/>
    <stringAttribute key="checked" value="[NONE]"/>
    <booleanAttribute key="clearConfig" value="true"/>
    <booleanAttribute key="clearws" value="false"/>
    <booleanAttribute key="clearwslog" value="false"/>
    <stringAttribute key="configLocation" value="${workspace_loc}/.metadata/.plugins/org.eclipse.pde.core/polarion"/>
    <booleanAttribute key="default" value="false"/>
    <setAttribute key="deselected_workspace_bundles">
        <setEntry value="com.fasnote.alm.plugin.manage"/>
    </setAttribute>
    <booleanAttribute key="includeOptional" value="true"/>
    <stringAttribute key="location" value="/opt/polarion/data/workspace"/>
    <booleanAttribute key="org.eclipse.debug.core.ATTR_FORCE_SYSTEM_CONSOLE_ENCODING" value="false"/>
    <booleanAttribute key="org.eclipse.jdt.launching.ATTR_ATTR_USE_ARGFILE" value="false"/>
    <booleanAttribute key="org.eclipse.jdt.launching.ATTR_SHOW_CODEDETAILS_IN_EXCEPTION_MESSAGES" value="true"/>
    <booleanAttribute key="org.eclipse.jdt.launching.ATTR_USE_START_ON_FIRST_THREAD" value="true"/>
    <listAttribute key="org.eclipse.jdt.launching.CLASSPATH">
        <listEntry value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;runtimeClasspathEntry containerPath=&quot;org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-11&quot; path=&quot;4&quot; type=&quot;4&quot;/&gt;&#10;"/>
    </listAttribute>
    <booleanAttribute key="org.eclipse.jdt.launching.DEFAULT_CLASSPATH" value="true"/>
    <stringAttribute key="org.eclipse.jdt.launching.JRE_CONTAINER" value="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-11"/>
    <stringAttribute key="org.eclipse.jdt.launching.PROGRAM_ARGUMENTS" value="-os linux -ws linux -arch arm64 -appId polarion.server"/>
    <stringAttribute key="org.eclipse.jdt.launching.SOURCE_PATH_PROVIDER" value="org.eclipse.pde.ui.workbenchClasspathProvider"/>
    <stringAttribute key="org.eclipse.jdt.launching.VM_ARGUMENTS" value="-Dcom.polarion.home=/opt/polarion/polarion&#10;-Dcom.polarion.propertyFile=/opt/polarion/etc/polarion.properties"/>
    <stringAttribute key="pde.version" value="3.3"/>
    <stringAttribute key="product" value=""/>
    <setAttribute key="selected_target_bundles">
        <setEntry value="antlr4-runtime@default:default"/>
        <setEntry value="antlr4@default:default"/>
        <setEntry value="antlr@default:default"/>
        <setEntry value="bcprov@default:default"/>
        <setEntry value="com.auth0.java-jwt@default:default"/>
        <setEntry value="com.fasterxml.classmate@default:default"/>
        <setEntry value="com.fasterxml.jackson.dataformat.jackson-dataformat-yaml@default:default"/>
        <setEntry value="com.fasterxml.jackson.jaxrs@default:default"/>
        <setEntry value="com.fasterxml.jackson@default:default"/>
        <setEntry value="com.fasterxml.woodstox@default:default"/>
        <setEntry value="com.google.gson@default:default"/>
        <setEntry value="com.google.guava.failureaccess@default:default"/>
        <setEntry value="com.google.guava@default:default"/>
        <setEntry value="com.ibm.icu.icu4j@default:default"/>
        <setEntry value="com.icl.saxon@default:default"/>
        <setEntry value="com.jayway.jsonpath.json-path@default:default"/>
        <setEntry value="com.jcraft.jsch@default:default"/>
        <setEntry value="com.networknt.json-schema-validator@default:default"/>
        <setEntry value="com.nimbusds.content-type@default:default"/>
        <setEntry value="com.nimbusds.nimbus-jose-jwt@default:default"/>
        <setEntry value="com.opensymphony.quartz@default:default"/>
        <setEntry value="com.polarion.alm.builder@default:default"/>
        <setEntry value="com.polarion.alm.checker@default:default"/>
        <setEntry value="com.polarion.alm.impex@default:default"/>
        <setEntry value="com.polarion.alm.install@default:default"/>
        <setEntry value="com.polarion.alm.oslc@default:default"/>
        <setEntry value="com.polarion.alm.projects@default:default"/>
        <setEntry value="com.polarion.alm.qcentre@default:default"/>
        <setEntry value="com.polarion.alm.tracker@default:default"/>
        <setEntry value="com.polarion.alm.ui.diagrams.mxgraph@default:default"/>
        <setEntry value="com.polarion.alm.ui@default:default"/>
        <setEntry value="com.polarion.alm.wiki@default:default"/>
        <setEntry value="com.polarion.alm.ws.client@default:default"/>
        <setEntry value="com.polarion.alm.ws@default:default"/>
        <setEntry value="com.polarion.cluster@default:default"/>
        <setEntry value="com.polarion.core.boot@default:default"/>
        <setEntry value="com.polarion.core.util@default:default"/>
        <setEntry value="com.polarion.fop@default:default"/>
        <setEntry value="com.polarion.platform.guice@default:default"/>
        <setEntry value="com.polarion.platform.hivemind@default:default"/>
        <setEntry value="com.polarion.platform.jobs@default:default"/>
        <setEntry value="com.polarion.platform.monitoring@default:default"/>
        <setEntry value="com.polarion.platform.persistence@default:default"/>
        <setEntry value="com.polarion.platform.repository.driver.svn@default:default"/>
        <setEntry value="com.polarion.platform.repository.external.git@default:default"/>
        <setEntry value="com.polarion.platform.repository.external.svn@default:default"/>
        <setEntry value="com.polarion.platform.repository.external@default:default"/>
        <setEntry value="com.polarion.platform.repository@default:default"/>
        <setEntry value="com.polarion.platform.sql@default:default"/>
        <setEntry value="com.polarion.platform@default:default"/>
        <setEntry value="com.polarion.portal.tomcat@default:default"/>
        <setEntry value="com.polarion.psvn.launcher@default:default"/>
        <setEntry value="com.polarion.psvn.translations.en@default:default"/>
        <setEntry value="com.polarion.purevariants@default:default"/>
        <setEntry value="com.polarion.qcentre@default:default"/>
        <setEntry value="com.polarion.scripting.servlet@default:default"/>
        <setEntry value="com.polarion.scripting@default:default"/>
        <setEntry value="com.polarion.subterra.base@default:default"/>
        <setEntry value="com.polarion.subterra.index@default:default"/>
        <setEntry value="com.polarion.subterra.persistence.document@default:default"/>
        <setEntry value="com.polarion.subterra.persistence@default:default"/>
        <setEntry value="com.polarion.synchronizer.proxy.hpalm@default:default"/>
        <setEntry value="com.polarion.synchronizer.proxy.jira@default:default"/>
        <setEntry value="com.polarion.synchronizer.proxy.polarion@default:default"/>
        <setEntry value="com.polarion.synchronizer.proxy.reqif@default:default"/>
        <setEntry value="com.polarion.synchronizer.ui@default:default"/>
        <setEntry value="com.polarion.synchronizer@default:default"/>
        <setEntry value="com.polarion.usdp.persistence@default:default"/>
        <setEntry value="com.polarion.xray.doc.user@default:default"/>
        <setEntry value="com.siemens.des.logger.api@default:default"/>
        <setEntry value="com.siemens.plm.bitools.analytics@default:default"/>
        <setEntry value="com.siemens.polarion.ct.collectors.git@default:default"/>
        <setEntry value="com.siemens.polarion.eclipse.configurator@default:default"/>
        <setEntry value="com.siemens.polarion.integration.ci@default:default"/>
        <setEntry value="com.siemens.polarion.previewer.external@default:default"/>
        <setEntry value="com.siemens.polarion.previewer@default:default"/>
        <setEntry value="com.siemens.polarion.rest@default:default"/>
        <setEntry value="com.siemens.polarion.rt.api@default:default"/>
        <setEntry value="com.siemens.polarion.rt.collectors.git@default:default"/>
        <setEntry value="com.siemens.polarion.rt.communication.common@default:default"/>
        <setEntry value="com.siemens.polarion.rt.communication.polarion@default:default"/>
        <setEntry value="com.siemens.polarion.rt.communication.rt@default:default"/>
        <setEntry value="com.siemens.polarion.rt.parsers.c@default:default"/>
        <setEntry value="com.siemens.polarion.rt.ui@default:default"/>
        <setEntry value="com.siemens.polarion.rt@default:default"/>
        <setEntry value="com.siemens.polarion.synchronizer.proxy.tfs@default:default"/>
        <setEntry value="com.sun.activation.javax.activation@default:default"/>
        <setEntry value="com.sun.istack.commons-runtime@default:default"/>
        <setEntry value="com.sun.jna.platform@default:default"/>
        <setEntry value="com.sun.jna@default:default"/>
        <setEntry value="com.sun.xml.bind.jaxb-impl@default:false"/>
        <setEntry value="com.trilead.ssh2@default:default"/>
        <setEntry value="com.zaxxer.hikariCP@default:default"/>
        <setEntry value="des-sdk-core@default:default"/>
        <setEntry value="des-sdk-dss@default:default"/>
        <setEntry value="io.github.resilience4j.circuitbreaker@default:default"/>
        <setEntry value="io.github.resilience4j.core@default:default"/>
        <setEntry value="io.github.resilience4j.retry@default:default"/>
        <setEntry value="io.swagger@default:default"/>
        <setEntry value="io.vavr@default:default"/>
        <setEntry value="jakaroma@default:default"/>
        <setEntry value="jakarta.validation.validation-api@default:default"/>
        <setEntry value="javassist@default:default"/>
        <setEntry value="javax.annotation-api@default:default"/>
        <setEntry value="javax.cache@default:default"/>
        <setEntry value="javax.el@default:default"/>
        <setEntry value="javax.inject@default:default"/>
        <setEntry value="javax.servlet.jsp@default:default"/>
        <setEntry value="javax.servlet@default:default"/>
        <setEntry value="javax.transaction@default:default"/>
        <setEntry value="jaxb-api@default:default"/>
        <setEntry value="jcip-annotations@default:default"/>
        <setEntry value="jcl.over.slf4j@default:default"/>
        <setEntry value="jul.to.slf4j@default:default"/>
        <setEntry value="kuromoji-core@default:default"/>
        <setEntry value="kuromoji-ipadic@default:default"/>
        <setEntry value="lang-tag@default:default"/>
        <setEntry value="net.htmlparser.jericho@default:default"/>
        <setEntry value="net.java.dev.jna@default:default"/>
        <setEntry value="net.minidev.accessors-smart@default:default"/>
        <setEntry value="net.minidev.asm@default:default"/>
        <setEntry value="net.minidev.json-smart@default:default"/>
        <setEntry value="net.n3.nanoxml@default:default"/>
        <setEntry value="net.sourceforge.cssparser@default:default"/>
        <setEntry value="nu.xom@default:default"/>
        <setEntry value="oauth2-oidc-sdk@default:default"/>
        <setEntry value="org.apache.ant@default:default"/>
        <setEntry value="org.apache.avro@default:default"/>
        <setEntry value="org.apache.axis@default:default"/>
        <setEntry value="org.apache.batik@default:default"/>
        <setEntry value="org.apache.commons.codec@default:default"/>
        <setEntry value="org.apache.commons.collections@default:default"/>
        <setEntry value="org.apache.commons.commons-beanutils@default:default"/>
        <setEntry value="org.apache.commons.commons-collections4@default:default"/>
        <setEntry value="org.apache.commons.commons-compress@default:default"/>
        <setEntry value="org.apache.commons.commons-fileupload@default:default"/>
        <setEntry value="org.apache.commons.digester@default:default"/>
        <setEntry value="org.apache.commons.exec@default:default"/>
        <setEntry value="org.apache.commons.io@default:default"/>
        <setEntry value="org.apache.commons.lang3@default:default"/>
        <setEntry value="org.apache.commons.lang@default:default"/>
        <setEntry value="org.apache.commons.logging@default:default"/>
        <setEntry value="org.apache.curator@default:default"/>
        <setEntry value="org.apache.fop@default:default"/>
        <setEntry value="org.apache.hivemind@default:default"/>
        <setEntry value="org.apache.httpcomponents.httpclient@default:default"/>
        <setEntry value="org.apache.httpcomponents.httpcore@default:default"/>
        <setEntry value="org.apache.jasper.glassfish@default:default"/>
        <setEntry value="org.apache.kafka.clients@default:default"/>
        <setEntry value="org.apache.kafka.streams@default:default"/>
        <setEntry value="org.apache.logging.log4j.1.2-api@default:false"/>
        <setEntry value="org.apache.logging.log4j.api@default:default"/>
        <setEntry value="org.apache.logging.log4j.apiconf@default:false"/>
        <setEntry value="org.apache.logging.log4j.core@default:default"/>
        <setEntry value="org.apache.logging.log4j.slf4j-impl@default:false"/>
        <setEntry value="org.apache.lucene.analyzers-common*7.1.0.v20180122-2126@default:default"/>
        <setEntry value="org.apache.lucene.analyzers-common*7.2.1@default:default"/>
        <setEntry value="org.apache.lucene.analyzers-smartcn@default:default"/>
        <setEntry value="org.apache.lucene.core*7.1.0.v20171214-1510@default:default"/>
        <setEntry value="org.apache.lucene.core*7.2.1@default:default"/>
        <setEntry value="org.apache.lucene.grouping@default:default"/>
        <setEntry value="org.apache.lucene.queryparser@default:default"/>
        <setEntry value="org.apache.oro@default:default"/>
        <setEntry value="org.apache.pdfbox.fontbox@default:default"/>
        <setEntry value="org.apache.poi@default:default"/>
        <setEntry value="org.apache.tika@default:default"/>
        <setEntry value="org.apache.xalan@default:default"/>
        <setEntry value="org.apache.xercesImpl@default:default"/>
        <setEntry value="org.apache.xml.serializer@default:default"/>
        <setEntry value="org.apache.xmlgraphics.commons@default:default"/>
        <setEntry value="org.apache.zookeeper@default:default"/>
        <setEntry value="org.codehaus.groovy@default:default"/>
        <setEntry value="org.codehaus.jettison@default:default"/>
        <setEntry value="org.dom4j@default:default"/>
        <setEntry value="org.eclipse.core.contenttype@default:default"/>
        <setEntry value="org.eclipse.core.expressions@default:default"/>
        <setEntry value="org.eclipse.core.filesystem@default:default"/>
        <setEntry value="org.eclipse.core.jobs@default:default"/>
        <setEntry value="org.eclipse.core.net@default:default"/>
        <setEntry value="org.eclipse.core.resources@default:default"/>
        <setEntry value="org.eclipse.core.runtime@default:true"/>
        <setEntry value="org.eclipse.equinox.app@default:default"/>
        <setEntry value="org.eclipse.equinox.common@2:true"/>
        <setEntry value="org.eclipse.equinox.event@default:default"/>
        <setEntry value="org.eclipse.equinox.http.registry@default:default"/>
        <setEntry value="org.eclipse.equinox.http.servlet@default:default"/>
        <setEntry value="org.eclipse.equinox.jsp.jasper.registry@default:default"/>
        <setEntry value="org.eclipse.equinox.jsp.jasper@default:default"/>
        <setEntry value="org.eclipse.equinox.launcher@default:default"/>
        <setEntry value="org.eclipse.equinox.preferences@default:default"/>
        <setEntry value="org.eclipse.equinox.registry@default:default"/>
        <setEntry value="org.eclipse.equinox.security@default:default"/>
        <setEntry value="org.eclipse.equinox.simpleconfigurator@1:true"/>
        <setEntry value="org.eclipse.help.base@default:default"/>
        <setEntry value="org.eclipse.help.webapp@default:default"/>
        <setEntry value="org.eclipse.help@default:default"/>
        <setEntry value="org.eclipse.jgit@default:default"/>
        <setEntry value="org.eclipse.osgi.services@default:default"/>
        <setEntry value="org.eclipse.osgi.util@default:default"/>
        <setEntry value="org.eclipse.osgi@1:true"/>
        <setEntry value="org.ehcache@default:default"/>
        <setEntry value="org.gitlab.java-gitlab-api@default:default"/>
        <setEntry value="org.glassfish.jersey@default:default"/>
        <setEntry value="org.hibernate.annotations@default:default"/>
        <setEntry value="org.hibernate.core@default:default"/>
        <setEntry value="org.hibernate.entitymanager@default:default"/>
        <setEntry value="org.hibernate.hikaricp@default:default"/>
        <setEntry value="org.hibernate.jpa.2.1.api@default:default"/>
        <setEntry value="org.jboss.logging@default:default"/>
        <setEntry value="org.jvnet.mimepull@default:default"/>
        <setEntry value="org.objectweb.asm@default:default"/>
        <setEntry value="org.objectweb.jotm@default:default"/>
        <setEntry value="org.opensaml@default:default"/>
        <setEntry value="org.polarion.svncommons@default:default"/>
        <setEntry value="org.polarion.svnwebclient@default:default"/>
        <setEntry value="org.postgesql@default:default"/>
        <setEntry value="org.rocksdb.rocksdbjni@default:default"/>
        <setEntry value="org.springframework.data.core@default:default"/>
        <setEntry value="org.springframework.data.jpa@default:default"/>
        <setEntry value="org.springframework.spring-aop@default:default"/>
        <setEntry value="org.springframework.spring-beans@default:default"/>
        <setEntry value="org.springframework.spring-context@default:default"/>
        <setEntry value="org.springframework.spring-core@default:default"/>
        <setEntry value="org.springframework.spring-expression@default:default"/>
        <setEntry value="org.springframework.spring-jdbc@default:default"/>
        <setEntry value="org.springframework.spring-orm@default:default"/>
        <setEntry value="org.springframework.spring-test@default:default"/>
        <setEntry value="org.springframework.spring-tx@default:default"/>
        <setEntry value="org.springframework.spring-web@default:default"/>
        <setEntry value="org.springframework.spring-webmvc@default:default"/>
        <setEntry value="org.tmatesoft.sqljet@default:default"/>
        <setEntry value="org.tmatesoft.svnkit@default:default"/>
        <setEntry value="saaj-api@default:default"/>
        <setEntry value="sdk-lifecycle-collab@default:default"/>
        <setEntry value="sdk-lifecycle-docmgmt@default:default"/>
        <setEntry value="siemens.des.clientsecurity@default:default"/>
        <setEntry value="slf4j.api@default:default"/>
        <setEntry value="xml-apis@default:default"/>
        <setEntry value="xml.apis.ext@default:default"/>
        <setEntry value="xstream@default:default"/>
    </setAttribute>
    <setAttribute key="selected_workspace_bundles">
        <setEntry value="com.fasnote.alm.auth.feishu@default:default"/>
        <setEntry value="com.fasnote.alm.gitlab@default:default"/>
        <setEntry value="com.fasnote.alm.license@default:default"/>
        <setEntry value="com.polarion.alm.ProjectPlanGantt_new@default:default"/>
    </setAttribute>
    <booleanAttribute key="show_selected_only" value="false"/>
    <stringAttribute key="templateConfig" value="${target_home}/configuration/config.ini"/>
    <booleanAttribute key="tracing" value="false"/>
    <booleanAttribute key="useCustomFeatures" value="false"/>
    <booleanAttribute key="useDefaultConfig" value="true"/>
    <booleanAttribute key="useDefaultConfigArea" value="true"/>
    <booleanAttribute key="useProduct" value="false"/>
</launchConfiguration>
