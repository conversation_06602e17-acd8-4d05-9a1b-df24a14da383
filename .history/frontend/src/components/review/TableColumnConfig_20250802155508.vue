<template>
  <div class="table-column-config">
      <div class="config-content">
        <!-- 配置方案管理 -->
        <div class="config-schemes">
          <div class="scheme-header">
            <h4>配置方案</h4>
            <div class="scheme-actions">
              <el-select
                v-model="currentSchemeId"
                placeholder="选择配置方案"
                @change="handleSchemeChange"
                style="width: 200px"
              >
                <el-option
                  v-for="scheme in schemes"
                  :key="scheme.id"
                  :label="scheme.name"
                  :value="scheme.id"
                />
              </el-select>
              <el-button @click="showNewSchemeDialog = true">
                <el-icon><Plus /></el-icon>
                新建方案
              </el-button>
            </div>
          </div>
        </div>

        <!-- 列配置区域 -->
        <div class="columns-config">
          <div class="config-section">
            <h4>基础列（不可删除）</h4>
            <div class="columns-list">
              <draggable
                v-model="fixedColumns"
                item-key="id"
                :disabled="true"
                class="draggable-list"
              >
                <template #item="{ element: column }">
                  <div class="column-item fixed">
                    <div class="column-info">
                      <el-icon class="drag-handle disabled"><Rank /></el-icon>
                      <span class="column-label">{{ column.label }}</span>
                      <el-tag size="small" type="info">固定</el-tag>
                    </div>
                    <div class="column-controls">
                      <el-switch
                        v-model="column.visible"
                        size="small"
                        :disabled="column.key === 'selection' || column.key === 'sequence' || column.key === 'content'"
                      />
                      <el-input-number
                        v-if="column.width"
                        v-model="column.width"
                        :min="60"
                        :max="500"
                        size="small"
                        style="width: 80px"
                      />
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
          </div>

          <div class="config-section">
            <h4>
              自定义列
              <el-button size="small" @click="showAddColumnDialog = true">
                <el-icon><Plus /></el-icon>
                添加列
              </el-button>
            </h4>
            <div class="columns-list">
              <draggable
                v-model="customColumns"
                item-key="id"
                handle=".drag-handle"
                @end="handleDragEnd"
                class="draggable-list"
              >
                <template #item="{ element: column, index }">
                  <div class="column-item">
                    <div class="column-info">
                      <el-icon class="drag-handle"><Rank /></el-icon>
                      <span class="column-label">{{ column.label }}</span>
                      <el-tag size="small" :type="getColumnTypeTag(column.type)">
                        {{ getColumnTypeText(column.type) }}
                      </el-tag>
                    </div>
                    <div class="column-controls">
                      <el-switch v-model="column.visible" size="small" />
                      <el-input-number
                        v-if="column.width"
                        v-model="column.width"
                        :min="60"
                        :max="500"
                        size="small"
                        style="width: 80px"
                      />
                      <el-button
                        size="small"
                        @click="editColumn(column, index)"
                      >
                        <el-icon><Edit /></el-icon>
                      </el-button>
                      <el-button
                        size="small"
                        type="danger"
                        @click="removeColumn(index)"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
          </div>
        </div>

        <!-- 分组配置 -->
        <div class="group-config">
          <h4>分组设置</h4>
          <div class="group-settings">
            <el-form :model="groupSettings" label-width="120px">
              <el-form-item label="分组字段">
                <el-select
                  v-model="groupSettings.groupBy"
                  placeholder="选择分组字段"
                  clearable
                  style="width: 200px"
                >
                  <el-option
                    v-for="column in groupableColumns"
                    :key="column.key"
                    :label="column.label"
                    :value="column.key"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="显示统计">
                <el-switch v-model="groupSettings.showGroupSummary" />
              </el-form-item>
              <el-form-item label="默认折叠">
                <el-switch v-model="groupSettings.defaultCollapsed" />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button @click="resetToDefault">重置为默认</el-button>
          <el-button type="primary" @click="saveConfig">保存配置</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新建方案对话框 -->
    <el-dialog
      v-model="showNewSchemeDialog"
      title="新建配置方案"
      width="400px"
    >
      <el-form :model="newScheme" label-width="80px">
        <el-form-item label="方案名称" required>
          <el-input v-model="newScheme.name" placeholder="请输入方案名称" />
        </el-form-item>
        <el-form-item label="基于方案">
          <el-select v-model="newScheme.baseSchemeId" placeholder="选择基础方案">
            <el-option
              v-for="scheme in schemes"
              :key="scheme.id"
              :label="scheme.name"
              :value="scheme.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showNewSchemeDialog = false">取消</el-button>
        <el-button type="primary" @click="createNewScheme">创建</el-button>
      </template>
    </el-dialog>

    <!-- 添加/编辑列对话框 -->
    <el-dialog
      v-model="showAddColumnDialog"
      :title="editingColumn ? '编辑列' : '添加列'"
      width="500px"
    >
      <el-form :model="columnForm" :rules="columnRules" ref="columnFormRef" label-width="100px">
        <el-form-item label="列名称" prop="label">
          <el-input v-model="columnForm.label" placeholder="请输入列名称" />
        </el-form-item>
        <el-form-item label="字段键" prop="key">
          <el-input v-model="columnForm.key" placeholder="请输入字段键" />
        </el-form-item>
        <el-form-item label="列类型" prop="type">
          <el-select v-model="columnForm.type" placeholder="选择列类型">
            <el-option label="文本" value="text" />
            <el-option label="数字" value="number" />
            <el-option label="日期" value="date" />
            <el-option label="标签" value="tag" />
          </el-select>
        </el-form-item>
        <el-form-item label="列宽度">
          <el-input-number v-model="columnForm.width" :min="60" :max="500" />
        </el-form-item>
        <el-form-item label="可排序">
          <el-switch v-model="columnForm.sortable" />
        </el-form-item>
        <el-form-item label="可编辑">
          <el-switch v-model="columnForm.editable" />
        </el-form-item>
        <el-form-item v-if="columnForm.type === 'tag'" label="选项">
          <el-input
            v-model="optionsText"
            type="textarea"
            placeholder="每行一个选项"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="cancelColumnEdit">取消</el-button>
        <el-button type="primary" @click="saveColumn">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { Plus, Rank, Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import draggable from 'vuedraggable'
import {
  type ColumnConfig,
  type TableViewConfig,
  type CustomFieldDefinition,
  DEFAULT_COLUMNS,
  DEFAULT_TABLE_CONFIG,
  createCustomColumn
} from '@/types/table-config'

// Props
interface Props {
  modelValue: boolean
  currentConfig: TableViewConfig
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'configChange', config: TableViewConfig): void
  (e: 'schemeChange', schemeId: string): void
}

const emit = defineEmits<Emits>()

// Reactive data
const visible = ref(false)
const schemes = ref<TableViewConfig[]>([DEFAULT_TABLE_CONFIG])
const currentSchemeId = ref('default')
const fixedColumns = ref<ColumnConfig[]>([])
const customColumns = ref<ColumnConfig[]>([])
const groupSettings = ref({
  groupBy: '',
  showGroupSummary: true,
  defaultCollapsed: false
})

const showNewSchemeDialog = ref(false)
const newScheme = ref({
  name: '',
  baseSchemeId: 'default'
})

const showAddColumnDialog = ref(false)
const editingColumn = ref<ColumnConfig | null>(null)
const editingIndex = ref(-1)
const columnForm = ref({
  label: '',
  key: '',
  type: 'text',
  width: 150,
  sortable: true,
  editable: false
})
const optionsText = ref('')
const columnFormRef = ref()

// Validation rules
const columnRules = {
  label: [{ required: true, message: '请输入列名称', trigger: 'blur' }],
  key: [{ required: true, message: '请输入字段键', trigger: 'blur' }],
  type: [{ required: true, message: '请选择列类型', trigger: 'change' }]
}

// Methods
const loadCurrentConfig = () => {
  const config = props.currentConfig
  console.log('TableColumnConfig 加载配置:', config)

  fixedColumns.value = config.columns.filter(col => col.fixed)
  customColumns.value = config.columns.filter(col => !col.fixed)

  console.log('固定列:', fixedColumns.value.length, '自定义列:', customColumns.value.length)

  groupSettings.value = {
    groupBy: config.groupBy || '',
    showGroupSummary: config.showGroupSummary || false,
    defaultCollapsed: config.defaultCollapsed || false
  }
}

// Computed
const groupableColumns = computed(() => {
  return [...fixedColumns.value, ...customColumns.value].filter(col =>
    col.type === 'tag' || col.type === 'text'
  )
})

// Watch
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    loadCurrentConfig()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

watch(() => props.currentConfig, () => {
  loadCurrentConfig()
}, { immediate: true, deep: true })

const handleSchemeChange = (schemeId: string) => {
  const scheme = schemes.value.find(s => s.id === schemeId)
  if (scheme) {
    emit('schemeChange', schemeId)
  }
}

const createNewScheme = () => {
  if (!newScheme.value.name.trim()) {
    ElMessage.warning('请输入方案名称')
    return
  }
  
  const baseScheme = schemes.value.find(s => s.id === newScheme.value.baseSchemeId)
  const newSchemeConfig: TableViewConfig = {
    id: `scheme_${Date.now()}`,
    name: newScheme.value.name,
    columns: baseScheme ? [...baseScheme.columns] : [...DEFAULT_COLUMNS],
    groupBy: baseScheme?.groupBy,
    sortBy: baseScheme?.sortBy,
    sortOrder: baseScheme?.sortOrder,
    showGroupSummary: baseScheme?.showGroupSummary,
    defaultCollapsed: baseScheme?.defaultCollapsed
  }
  
  schemes.value.push(newSchemeConfig)
  currentSchemeId.value = newSchemeConfig.id
  showNewSchemeDialog.value = false
  newScheme.value = { name: '', baseSchemeId: 'default' }
  
  ElMessage.success('配置方案创建成功')
}

const editColumn = (column: ColumnConfig, index: number) => {
  editingColumn.value = column
  editingIndex.value = index
  columnForm.value = {
    label: column.label,
    key: column.key,
    type: column.type,
    width: column.width || 150,
    sortable: column.sortable || false,
    editable: column.editable || false
  }
  optionsText.value = column.options?.join('\n') || ''
  showAddColumnDialog.value = true
}

const removeColumn = async (index: number) => {
  try {
    await ElMessageBox.confirm('确认删除此列？', '确认删除', {
      type: 'warning'
    })
    customColumns.value.splice(index, 1)
  } catch {
    // User cancelled
  }
}

const saveColumn = async () => {
  try {
    await columnFormRef.value.validate()
    
    const columnData: ColumnConfig = {
      id: editingColumn.value?.id || `custom_${Date.now()}`,
      key: columnForm.value.key,
      label: columnForm.value.label,
      width: columnForm.value.width,
      type: columnForm.value.type as any,
      visible: true,
      fixed: false,
      sortable: columnForm.value.sortable,
      editable: columnForm.value.editable,
      options: columnForm.value.type === 'tag' ? 
        optionsText.value.split('\n').filter(opt => opt.trim()) : undefined
    }
    
    if (editingColumn.value) {
      // 编辑现有列
      customColumns.value[editingIndex.value] = columnData
    } else {
      // 添加新列
      customColumns.value.push(columnData)
    }
    
    cancelColumnEdit()
    ElMessage.success(editingColumn.value ? '列更新成功' : '列添加成功')
  } catch {
    // Validation failed
  }
}

const cancelColumnEdit = () => {
  showAddColumnDialog.value = false
  editingColumn.value = null
  editingIndex.value = -1
  columnForm.value = {
    label: '',
    key: '',
    type: 'text',
    width: 150,
    sortable: true,
    editable: false
  }
  optionsText.value = ''
}

const handleDragEnd = () => {
  // 拖拽结束后的处理
}

const resetToDefault = async () => {
  try {
    await ElMessageBox.confirm('确认重置为默认配置？', '确认重置', {
      type: 'warning'
    })
    
    fixedColumns.value = DEFAULT_COLUMNS.filter(col => col.fixed)
    customColumns.value = DEFAULT_COLUMNS.filter(col => !col.fixed)
    groupSettings.value = {
      groupBy: DEFAULT_TABLE_CONFIG.groupBy || '',
      showGroupSummary: DEFAULT_TABLE_CONFIG.showGroupSummary || false,
      defaultCollapsed: DEFAULT_TABLE_CONFIG.defaultCollapsed || false
    }
    
    ElMessage.success('已重置为默认配置')
  } catch {
    // User cancelled
  }
}

const saveConfig = () => {
  const config: TableViewConfig = {
    id: currentSchemeId.value,
    name: schemes.value.find(s => s.id === currentSchemeId.value)?.name || '默认视图',
    columns: [...fixedColumns.value, ...customColumns.value],
    groupBy: groupSettings.value.groupBy || undefined,
    showGroupSummary: groupSettings.value.showGroupSummary,
    defaultCollapsed: groupSettings.value.defaultCollapsed
  }
  
  emit('configChange', config)
  handleClose()
  ElMessage.success('配置保存成功')
}

const handleClose = () => {
  visible.value = false
}

// Utility methods
const getColumnTypeTag = (type: string) => {
  const typeMap: Record<string, string> = {
    text: 'info',
    number: 'success',
    date: 'warning',
    tag: 'primary'
  }
  return typeMap[type] || 'info'
}

const getColumnTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    text: '文本',
    number: '数字',
    date: '日期',
    tag: '标签'
  }
  return typeMap[type] || type
}
</script>

<style scoped>
.table-column-config {
  .config-content {
    max-height: 600px;
    overflow-y: auto;
  }

  .config-schemes {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;
  }

  .scheme-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .scheme-header h4 {
    margin: 0;
    color: #303133;
  }

  .scheme-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .config-section {
    margin-bottom: 24px;
  }

  .config-section h4 {
    margin: 0 0 12px 0;
    color: #303133;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .columns-list {
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    overflow: hidden;
  }

  .column-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    background: #fff;
    transition: background-color 0.3s;
  }

  .column-item:last-child {
    border-bottom: none;
  }

  .column-item:hover {
    background: #f5f7fa;
  }

  .column-item.fixed {
    background: #fafbfc;
  }

  .column-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
  }

  .drag-handle {
    cursor: move;
    color: #909399;
  }

  .drag-handle.disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  .column-label {
    font-weight: 500;
    color: #303133;
  }

  .column-controls {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .group-config {
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;
  }

  .group-config h4 {
    margin: 0 0 16px 0;
    color: #303133;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>
