<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="90%"
    top="5vh"
    :before-close="handleClose"
    :close-on-click-modal="false"
    class="template-form-dialog"
  >
    <!-- 进度指示器 -->
    <div class="progress-indicator">
      <div class="progress-steps">
        <div
          v-for="(step, index) in progressSteps"
          :key="step.key"
          :class="['progress-step', {
            'completed': step.completed,
            'active': activeTab === step.key,
            'disabled': !canAccessTab(step.key),
            'required': step.required
          }]"
        >
          <div class="step-number">{{ index + 1 }}</div>
          <div class="step-label">{{ step.label }}</div>
          <div v-if="step.required" class="step-required">*</div>
        </div>
      </div>
    </div>

    <!-- 标签页导航 -->
    <el-tabs
      v-model="activeTab"
      type="border-card"
      class="template-tabs"
      @tab-change="handleTabChange"
    >
      <!-- 基本信息标签页 -->
      <el-tab-pane label="基本信息" name="basicInfo">
        <template #label>
          <div class="tab-label">
            <el-icon><InfoFilled /></el-icon>
            <span>基本信息</span>
            <el-badge v-if="!validation.basicInfo" is-dot type="danger" />
          </div>
        </template>

        <el-form
          ref="basicInfoFormRef"
          :model="templateConfig.basicInfo"
          :rules="basicInfoRules"
          label-width="100px"
          class="tab-form"
        >
          <div class="form-row">
            <el-form-item label="模板名称" prop="name" class="form-item-half">
              <el-input
                v-model="templateConfig.basicInfo.name"
                placeholder="请输入模板名称"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="模板类型" prop="type" class="form-item-half">
              <el-select
                v-model="templateConfig.basicInfo.type"
                placeholder="请选择或输入模板类型"
                filterable
                allow-create
                style="width: 100%"
              >
                <el-option
                  v-for="type in templateTypes"
                  :key="type"
                  :label="type"
                  :value="type"
                />
              </el-select>
            </el-form-item>
          </div>

          <div class="form-row">
            <el-form-item label="模板分类" prop="category" class="form-item-half">
              <el-input
                v-model="templateConfig.basicInfo.category"
                placeholder="请输入模板分类"
                maxlength="50"
              />
            </el-form-item>
            <el-form-item label="版本号" prop="version" class="form-item-half">
              <el-input
                v-model="templateConfig.basicInfo.version"
                placeholder="如：1.0.0"
                maxlength="20"
              />
            </el-form-item>
          </div>

          <el-form-item label="模板描述" prop="description">
            <el-input
              v-model="templateConfig.basicInfo.description"
              type="textarea"
              :rows="3"
              placeholder="请输入模板描述"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="标签">
            <el-tag
              v-for="tag in templateConfig.basicInfo.tags"
              :key="tag"
              closable
              @close="removeTag(tag)"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ tag }}
            </el-tag>
            <el-input
              v-if="tagInputVisible"
              ref="tagInputRef"
              v-model="tagInputValue"
              size="small"
              style="width: 100px;"
              @keyup.enter="addTag"
              @blur="addTag"
            />
            <el-button v-else size="small" @click="showTagInput">
              <el-icon><Plus /></el-icon>
              添加标签
            </el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- 表头配置标签页 -->
      <el-tab-pane label="表头配置" name="tableConfig" :disabled="!canAccessTab('tableConfig')">
        <template #label>
          <div class="tab-label" :class="{ disabled: !canAccessTab('tableConfig') }">
            <el-icon><Grid /></el-icon>
            <span>表头配置</span>
            <el-badge v-if="!validation.tableConfig" is-dot type="warning" />
            <el-icon v-if="!canAccessTab('tableConfig')" class="lock-icon"><Lock /></el-icon>
          </div>
        </template>

        <div class="tab-content">
          <div class="config-header">
            <div class="header-info">
              <h4>动态表头配置</h4>
              <p>配置评审表格的列显示、排序和自定义字段，这将影响检查项的可用字段</p>
            </div>
            <el-button type="primary" @click="openTableConfigDialog">
              <el-icon><Setting /></el-icon>
              {{ templateConfig.tableConfig ? '编辑表头配置' : '开始配置表头' }}
            </el-button>
          </div>

          <!-- 当前配置预览 -->
          <div v-if="templateConfig.tableConfig" class="config-preview">
            <div class="config-summary">
              <div class="summary-item">
                <span class="label">配置名称：</span>
                <span class="value">{{ templateConfig.tableConfig.name }}</span>
              </div>
              <div class="summary-item">
                <span class="label">显示列数：</span>
                <span class="value">{{ visibleColumnsCount }}</span>
              </div>
              <div class="summary-item">
                <span class="label">分组字段：</span>
                <span class="value">{{ templateConfig.tableConfig.groupBy || '无' }}</span>
              </div>
              <div class="summary-item">
                <span class="label">自定义列：</span>
                <span class="value">{{ customColumnsCount }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 检查项配置标签页 -->
      <el-tab-pane label="检查项配置" name="items" :disabled="!canAccessTab('items')">
        <template #label>
          <div class="tab-label" :class="{ disabled: !canAccessTab('items') }">
            <el-icon><List /></el-icon>
            <span>检查项配置</span>
            <el-badge v-if="!validation.items" is-dot type="danger" />
            <el-icon v-if="!canAccessTab('items')" class="lock-icon"><Lock /></el-icon>
          </div>
        </template>

        <div class="tab-content">
          <div class="section-header">
            <div class="header-info">
              <h4>检查项配置</h4>
              <p>配置具体的检查项内容，支持基于表头配置的动态字段</p>
            </div>
            <div class="section-actions">
              <el-button size="small" @click="handleAddItem">
                <el-icon><Plus /></el-icon>
                添加检查项
              </el-button>
              <el-button size="small" @click="handleBatchAdd">
                <el-icon><DocumentAdd /></el-icon>
                批量添加
              </el-button>
            </div>
          </div>

          <!-- Items list -->
          <div v-if="templateConfig.items.length > 0" class="items-container">
            <draggable
              v-model="templateConfig.items"
              item-key="tempId"
              handle=".drag-handle"
              @end="handleDragEnd"
            >
              <template #item="{ element: item, index }">
                <div class="item-card">
                  <div class="item-header">
                    <div class="item-info">
                      <el-icon class="drag-handle"><Rank /></el-icon>
                      <span class="item-sequence">{{ index + 1 }}</span>
                      <el-tag v-if="item.category" size="small" type="info">
                        {{ item.category }}
                      </el-tag>
                      <el-tag v-if="item.required" size="small" type="danger">
                        必填
                      </el-tag>
                      <!-- 显示动态字段标签 -->
                      <el-tag
                        v-for="(value, key) in item.dynamicFields"
                        :key="key"
                        size="small"
                        type="success"
                        v-if="value"
                      >
                        {{ getDynamicFieldLabel(key) }}: {{ value }}
                      </el-tag>
                    </div>
                    <div class="item-actions">
                      <el-button size="small" text @click="handleEditItem(index)">
                        <el-icon><Edit /></el-icon>
                      </el-button>
                      <el-button size="small" text type="danger" @click="handleDeleteItem(index)">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>
                  <div class="item-content">
                    {{ item.content }}
                  </div>
                </div>
              </template>
            </draggable>
          </div>

          <!-- Empty state -->
          <el-empty
            v-else
            description="暂无检查项"
            :image-size="80"
          >
            <el-button type="primary" @click="handleAddItem">
              添加第一个检查项
            </el-button>
          </el-empty>
        </div>
      </el-tab-pane>

      <!-- 缺陷规则标签页 -->
      <el-tab-pane label="缺陷规则" name="defectRules" :disabled="!canAccessTab('defectRules')">
        <template #label>
          <div class="tab-label" :class="{ disabled: !canAccessTab('defectRules') }">
            <el-icon><Warning /></el-icon>
            <span>缺陷规则</span>
            <el-badge v-if="!validation.defectRules" is-dot type="warning" />
            <el-icon v-if="!canAccessTab('defectRules')" class="lock-icon"><Lock /></el-icon>
          </div>
        </template>

        <div class="tab-content">
          <div class="config-header">
            <div class="header-info">
              <h4>缺陷生成规则配置</h4>
              <p>配置检查项不通过时自动生成缺陷的规则和模板</p>
            </div>
            <el-button type="primary" @click="handleAddDefectRule">
              <el-icon><Plus /></el-icon>
              添加缺陷规则
            </el-button>
          </div>

          <!-- 缺陷规则列表 -->
          <div v-if="templateConfig.defectRules.length > 0" class="rules-list">
            <div
              v-for="rule in templateConfig.defectRules"
              :key="rule.id"
              :class="['rule-item', { disabled: !rule.enabled }]"
            >
              <div class="rule-header">
                <div class="rule-info">
                  <div class="rule-title">
                    <h5>{{ rule.name }}</h5>
                    <el-switch
                      v-model="rule.enabled"
                      @change="handleRuleToggle(rule)"
                    />
                  </div>
                  <p class="rule-description">{{ rule.description }}</p>
                  <div class="rule-meta">
                    <el-tag
                      v-for="status in rule.trigger.status"
                      :key="status"
                      size="small"
                      type="warning"
                    >
                      触发状态: {{ getStatusText(status) }}
                    </el-tag>
                    <el-tag size="small" type="info">
                      {{ rule.options.autoGenerate ? '自动生成' : '手动确认' }}
                    </el-tag>
                  </div>
                </div>
                <div class="rule-actions">
                  <el-button size="small" @click="editDefectRule(rule)">编辑</el-button>
                  <el-button size="small" @click="duplicateDefectRule(rule)">复制</el-button>
                  <el-button size="small" type="danger" @click="deleteDefectRule(rule.id)">删除</el-button>
                </div>
              </div>
            </div>
          </div>
          <el-empty v-else description="暂无缺陷规则" :image-size="80">
            <el-button type="primary" @click="handleAddDefectRule">
              添加第一个规则
            </el-button>
          </el-empty>
        </div>
      </el-tab-pane>

      <!-- 状态按钮标签页 -->
      <el-tab-pane label="状态按钮" name="statusButtons" :disabled="!canAccessTab('statusButtons')">
        <template #label>
          <div class="tab-label" :class="{ disabled: !canAccessTab('statusButtons') }">
            <el-icon><Operation /></el-icon>
            <span>状态按钮</span>
            <el-badge v-if="!validation.statusButtons" is-dot type="warning" />
            <el-icon v-if="!canAccessTab('statusButtons')" class="lock-icon"><Lock /></el-icon>
          </div>
        </template>

        <div class="tab-content">
          <div class="config-header">
            <div class="header-info">
              <h4>状态按钮配置</h4>
              <p>配置检查项的状态按钮和对应的API接口</p>
              <el-text type="info" size="small">
                当前按钮组数量: {{ templateConfig.statusButtons.length }}
              </el-text>
            </div>
            <el-button type="primary" @click="handleAddButtonGroup">
              <el-icon><Plus /></el-icon>
              添加按钮组
            </el-button>
          </div>

          <!-- 按钮组列表 -->
          <div v-if="templateConfig.statusButtons.length > 0" class="button-groups-list">
            <div
              v-for="group in templateConfig.statusButtons"
              :key="group.id"
              class="group-item"
            >
              <div class="group-header">
                <div class="group-info">
                  <h5>{{ group.name }}</h5>
                  <p>{{ group.description }}</p>
                  <el-tag size="small">{{ group.buttons.length }} 个按钮</el-tag>
                </div>
                <div class="group-actions">
                  <el-button size="small" @click="handleAddStatusButton(group)">
                    <el-icon><Plus /></el-icon>
                    添加按钮
                  </el-button>
                  <el-button size="small" @click="editButtonGroup(group)">编辑</el-button>
                  <el-button size="small" @click="duplicateButtonGroup(group)">复制</el-button>
                  <el-button size="small" type="danger" @click="deleteButtonGroup(group.id)">删除</el-button>
                </div>
              </div>

              <!-- 按钮预览 -->
              <div class="buttons-preview">
                <div v-if="group.buttons && group.buttons.length > 0" class="buttons-list">
                  <div
                    v-for="button in group.buttons"
                    :key="button.id"
                    class="button-preview-item"
                  >
                    <div class="button-content">
                      <el-icon class="drag-handle"><Rank /></el-icon>
                      <el-button
                        :type="button.type"
                        size="small"
                        :disabled="!button.enabled"
                        @click="editStatusButton(group, button)"
                      >
                        <el-icon v-if="button.icon"><component :is="button.icon" /></el-icon>
                        {{ button.label }}
                      </el-button>
                    </div>
                    <div class="button-actions">
                      <el-button size="small" text @click="editStatusButton(group, button)">
                        <el-icon><Edit /></el-icon>
                      </el-button>
                      <el-button size="small" text type="danger" @click="deleteStatusButton(group, button.id)">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>
                </div>
                <div v-else class="no-buttons">
                  <el-text type="info" size="small">暂无按钮，点击上方"添加按钮"开始配置</el-text>
                </div>
              </div>
            </div>
          </div>
          <el-empty v-else description="暂无状态按钮组" :image-size="80">
            <el-button type="primary" @click="handleAddButtonGroup">
              添加第一个按钮组
            </el-button>
          </el-empty>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="saving" @click="handleSave">
          {{ mode === 'create' ? '创建' : '保存' }}
        </el-button>
      </div>
    </template>

    <!-- Item form dialog -->
    <el-dialog
      v-model="itemFormVisible"
      :title="itemFormMode === 'create' ? '添加检查项' : '编辑检查项'"
      width="600px"
      :before-close="handleItemFormClose"
    >
      <el-form
        ref="itemFormRef"
        :model="itemFormData"
        :rules="itemFormRules"
        label-width="80px"
      >
        <el-form-item label="检查内容" prop="content">
          <el-input
            v-model="itemFormData.content"
            type="textarea"
            :rows="3"
            placeholder="请输入检查项内容"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select
            v-model="itemFormData.category"
            placeholder="请选择或输入分类"
            filterable
            allow-create
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="category in itemCategories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否必填">
          <el-switch v-model="itemFormData.required" />
        </el-form-item>

        <!-- 动态字段 -->
        <div v-if="templateConfig.tableConfig && customColumnsCount > 0" class="dynamic-fields">
          <h4>自定义字段 ({{ customColumnsCount }} 个)</h4>
          <el-form-item
            v-for="column in templateConfig.tableConfig.columns.filter(c => !c.fixed && c.visible)"
            :key="column.id"
            :label="column.label"
          >
            <el-input
              v-if="column.type === 'text'"
              v-model="itemFormData.dynamicFields[column.key]"
              :placeholder="`请输入${column.label}`"
            />
            <el-input-number
              v-else-if="column.type === 'number'"
              v-model="itemFormData.dynamicFields[column.key]"
              :placeholder="`请输入${column.label}`"
              style="width: 100%"
            />
            <el-date-picker
              v-else-if="column.type === 'date'"
              v-model="itemFormData.dynamicFields[column.key]"
              type="date"
              :placeholder="`请选择${column.label}`"
              style="width: 100%"
            />
            <el-select
              v-else-if="column.type === 'tag' && column.options"
              v-model="itemFormData.dynamicFields[column.key]"
              :placeholder="`请选择${column.label}`"
              style="width: 100%"
            >
              <el-option
                v-for="option in column.options"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
            <el-input
              v-else
              v-model="itemFormData.dynamicFields[column.key]"
              :placeholder="`请输入${column.label}`"
            />
          </el-form-item>
        </div>
      </el-form>
      
      <template #footer>
        <el-button @click="handleItemFormClose">取消</el-button>
        <el-button type="primary" @click="handleItemSave">
          {{ itemFormMode === 'create' ? '添加' : '保存' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- Batch add dialog -->
    <el-dialog
      v-model="batchAddVisible"
      title="批量添加检查项"
      width="600px"
      :before-close="handleBatchAddClose"
    >
      <div class="batch-add-content">
        <el-alert
          title="批量添加说明"
          type="info"
          :closable="false"
          style="margin-bottom: 16px"
        >
          <p>每行一个检查项，支持以下格式：</p>
          <p>• 简单格式：检查项内容</p>
          <p>• 带分类：[分类名称] 检查项内容</p>
          <p>• 必填项：检查项内容 *</p>
          <p>• 完整格式：[分类名称] 检查项内容 *</p>
        </el-alert>
        
        <el-input
          v-model="batchAddText"
          type="textarea"
          :rows="10"
          placeholder="请输入检查项内容，每行一个..."
        />
        
        <div class="batch-preview" v-if="batchPreviewItems.length > 0">
          <h4>预览 ({{ batchPreviewItems.length }} 项)</h4>
          <div class="preview-list">
            <div
              v-for="(item, index) in batchPreviewItems"
              :key="index"
              class="preview-item"
            >
              <span class="preview-sequence">{{ index + 1 }}.</span>
              <span class="preview-content">{{ item.content }}</span>
              <el-tag v-if="item.category" size="small" type="info">
                {{ item.category }}
              </el-tag>
              <el-tag v-if="item.required" size="small" type="danger">
                必填
              </el-tag>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="handleBatchAddClose">取消</el-button>
        <el-button
          type="primary"
          :disabled="batchPreviewItems.length === 0"
          @click="handleBatchAddConfirm"
        >
          添加 {{ batchPreviewItems.length }} 项
        </el-button>
      </template>
    </el-dialog>

    <!-- 表头配置对话框 -->
    <el-dialog
      v-model="showTableConfigDialog"
      title="表头配置"
      width="80%"
      :before-close="handleTableConfigClose"
      :z-index="3000"
    >
      <TableColumnConfig
        :model-value="true"
        :current-config="templateConfig.tableConfig || DEFAULT_TABLE_CONFIG"
        @config-change="handleTableConfigChange"
        @update:model-value="handleTableConfigClose"
      />
    </el-dialog>

    <!-- 缺陷规则配置对话框 -->
    <el-dialog
      v-model="showDefectRuleDialog"
      :title="defectRuleMode === 'create' ? '添加缺陷规则' : '编辑缺陷规则'"
      width="70%"
      :before-close="handleDefectRuleClose"
    >
      <el-form
        ref="defectRuleFormRef"
        :model="defectRuleFormData"
        :rules="defectRuleFormRules"
        label-width="120px"
      >
        <el-tabs v-model="defectRuleActiveTab" type="border-card">
          <el-tab-pane label="基本配置" name="basic">
            <el-form-item label="规则名称" prop="name">
              <el-input
                v-model="defectRuleFormData.name"
                placeholder="请输入规则名称"
                maxlength="100"
              />
            </el-form-item>
            <el-form-item label="规则描述" prop="description">
              <el-input
                v-model="defectRuleFormData.description"
                type="textarea"
                :rows="3"
                placeholder="请输入规则描述"
                maxlength="500"
              />
            </el-form-item>
            <el-form-item label="启用状态">
              <el-switch v-model="defectRuleFormData.enabled" />
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="触发条件" name="trigger">
            <el-form-item label="触发状态" prop="trigger.status">
              <el-select
                v-model="defectRuleFormData.trigger.status"
                multiple
                placeholder="选择触发状态"
                style="width: 100%"
              >
                <el-option label="不通过" :value="ReviewItemStatus.FAIL" />
                <el-option label="跳过" :value="ReviewItemStatus.SKIP" />
              </el-select>
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="缺陷模板" name="template">
            <el-form-item label="标题模板" prop="template.titleTemplate">
              <el-input
                v-model="defectRuleFormData.template.titleTemplate"
                placeholder="如：{category} - {content}"
                maxlength="200"
              />
              <div class="form-tip">
                支持变量：{content}、{category}、{sequence}、{reviewer}
              </div>
            </el-form-item>
            <el-form-item label="描述模板" prop="template.descriptionTemplate">
              <el-input
                v-model="defectRuleFormData.template.descriptionTemplate"
                type="textarea"
                :rows="4"
                placeholder="请输入缺陷描述模板"
                maxlength="1000"
              />
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="生成选项" name="options">
            <el-form-item label="自动生成">
              <el-switch v-model="defectRuleFormData.options.autoGenerate" />
              <div class="form-tip">
                开启后将自动生成缺陷，否则需要手动确认
              </div>
            </el-form-item>
            <el-form-item label="需要确认">
              <el-switch v-model="defectRuleFormData.options.requireConfirmation" />
            </el-form-item>
            <el-form-item label="批量生成">
              <el-switch v-model="defectRuleFormData.options.batchGenerate" />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>

      <template #footer>
        <el-button @click="handleDefectRuleClose">取消</el-button>
        <el-button type="primary" @click="handleDefectRuleSave">
          {{ defectRuleMode === 'create' ? '添加' : '保存' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 状态按钮组配置对话框 -->
    <el-dialog
      v-model="showButtonGroupDialog"
      :title="buttonGroupMode === 'create' ? '添加按钮组' : '编辑按钮组'"
      width="60%"
      :before-close="handleButtonGroupClose"
    >
      <el-form
        ref="buttonGroupFormRef"
        :model="buttonGroupFormData"
        :rules="buttonGroupFormRules"
        label-width="100px"
      >
        <el-form-item label="组名称" prop="name">
          <el-input
            v-model="buttonGroupFormData.name"
            placeholder="请输入按钮组名称"
            maxlength="50"
          />
        </el-form-item>
        <el-form-item label="组描述" prop="description">
          <el-input
            v-model="buttonGroupFormData.description"
            type="textarea"
            :rows="2"
            placeholder="请输入按钮组描述"
            maxlength="200"
          />
        </el-form-item>
        <el-form-item label="布局方式">
          <el-radio-group v-model="buttonGroupFormData.layout">
            <el-radio value="horizontal">水平排列</el-radio>
            <el-radio value="vertical">垂直排列</el-radio>
            <el-radio value="grid">网格排列</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="handleButtonGroupClose">取消</el-button>
        <el-button type="primary" @click="handleButtonGroupSave">
          {{ buttonGroupMode === 'create' ? '添加' : '保存' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 状态按钮配置对话框 -->
    <el-dialog
      v-model="showStatusButtonDialog"
      :title="statusButtonMode === 'create' ? '添加状态按钮' : '编辑状态按钮'"
      width="60%"
      :before-close="handleStatusButtonClose"
    >
      <el-form
        ref="statusButtonFormRef"
        :model="statusButtonFormData"
        :rules="statusButtonFormRules"
        label-width="100px"
      >
        <el-tabs v-model="statusButtonActiveTab" type="border-card">
          <el-tab-pane label="基本配置" name="basic">
            <el-form-item label="按钮标签" prop="label">
              <el-input
                v-model="statusButtonFormData.label"
                placeholder="请输入按钮标签"
                maxlength="20"
              />
            </el-form-item>
            <el-form-item label="按钮状态" prop="status">
              <el-input
                v-model="statusButtonFormData.status"
                placeholder="如：CUSTOM_STATUS"
                maxlength="50"
              />
            </el-form-item>
            <el-form-item label="按钮类型" prop="type">
              <el-select v-model="statusButtonFormData.type" style="width: 100%">
                <el-option label="主要" value="primary" />
                <el-option label="成功" value="success" />
                <el-option label="警告" value="warning" />
                <el-option label="危险" value="danger" />
                <el-option label="信息" value="info" />
              </el-select>
            </el-form-item>
            <el-form-item label="图标">
              <el-input
                v-model="statusButtonFormData.icon"
                placeholder="如：Check"
                maxlength="50"
              />
            </el-form-item>
            <el-form-item label="启用状态">
              <el-switch v-model="statusButtonFormData.enabled" />
            </el-form-item>
            <el-form-item label="排序">
              <el-input-number
                v-model="statusButtonFormData.order"
                :min="1"
                :max="100"
                style="width: 100%"
              />
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="API配置" name="api">
            <el-form-item label="API接口" prop="action.apiEndpoint">
              <el-input
                v-model="statusButtonFormData.action.apiEndpoint"
                placeholder="如：/api/review/items/{id}/status"
                maxlength="200"
              />
            </el-form-item>
            <el-form-item label="请求方法" prop="action.method">
              <el-select v-model="statusButtonFormData.action.method" style="width: 100%">
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
                <el-option label="PATCH" value="PATCH" />
              </el-select>
            </el-form-item>
            <el-form-item label="需要备注">
              <el-switch v-model="statusButtonFormData.action.requireComment" />
            </el-form-item>
            <el-form-item label="确认消息">
              <el-input
                v-model="statusButtonFormData.action.confirmMessage"
                placeholder="如：确定要执行此操作吗？"
                maxlength="100"
              />
            </el-form-item>
            <el-form-item label="成功消息">
              <el-input
                v-model="statusButtonFormData.action.successMessage"
                placeholder="如：操作执行成功"
                maxlength="100"
              />
            </el-form-item>
            <el-form-item label="请求参数模板">
              <el-input
                v-model="payloadTemplateText"
                type="textarea"
                :rows="6"
                placeholder="JSON格式的请求参数模板，支持变量替换"
              />
              <div class="form-tip">
                支持变量：{id}、{status}、{comment}、{reviewer}等
              </div>
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="显示条件" name="conditions">
            <el-form-item label="当前状态条件">
              <el-select
                v-model="statusButtonFormData.displayConditions.currentStatus"
                multiple
                placeholder="选择可显示此按钮的当前状态"
                style="width: 100%"
              >
                <el-option label="待处理" :value="ReviewItemStatus.PENDING" />
                <el-option label="通过" :value="ReviewItemStatus.PASS" />
                <el-option label="不通过" :value="ReviewItemStatus.FAIL" />
                <el-option label="跳过" :value="ReviewItemStatus.SKIP" />
              </el-select>
              <div class="form-tip">
                只有当检查项处于选中状态时，此按钮才会显示
              </div>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>

      <template #footer>
        <el-button @click="handleStatusButtonClose">取消</el-button>
        <el-button type="primary" @click="handleStatusButtonSave">
          {{ statusButtonMode === 'create' ? '添加' : '保存' }}
        </el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  Plus,
  Edit,
  Delete,
  DocumentAdd,
  Rank,
  InfoFilled,
  Grid,
  List,
  Warning,
  Operation,
  Setting,
  Lock,
} from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import {
  createTemplate,
  updateTemplate,
  getTemplateTypes,
  type ChecklistTemplate,
} from '@/api/template'
import {
  type TemplateConfiguration,
  type ExtendedChecklistItem,
  type ValidationState,
  DEFAULT_TEMPLATE_CONFIG,
  createConfigFromTemplate,
  validateTemplateConfig,
  hasUnsavedChanges,
} from '@/types/template-config'
import {
  saveTemplateConfig,
  loadTemplateConfig,
} from '@/api/template-config'
import { type TableViewConfig, DEFAULT_TABLE_CONFIG } from '@/types/table-config'
import { type DefectRule } from '@/types/defect-config'
import { type StatusButtonGroup } from '@/types/defect-config'
import { ReviewItemStatus } from '@/api/review'
import TableColumnConfig from '@/components/review/TableColumnConfig.vue'

// Props
interface Props {
  visible: boolean
  template?: ChecklistTemplate | null
  mode: 'create' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  template: null,
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// Refs
const basicInfoFormRef = ref<FormInstance>()
const itemFormRef = ref<FormInstance>()
const tagInputRef = ref<HTMLInputElement>()

// Reactive data
const dialogVisible = ref(false)
const saving = ref(false)
const templateTypes = ref<string[]>([])
const activeTab = ref('basicInfo')

// 统一的模板配置数据
const templateConfig = ref<TemplateConfiguration>({ ...DEFAULT_TEMPLATE_CONFIG })
const originalConfig = ref<TemplateConfiguration>({ ...DEFAULT_TEMPLATE_CONFIG })

// 验证状态
const validation = ref<ValidationState>({
  basicInfo: false,
  tableConfig: true,
  items: false,
  defectRules: true,
  statusButtons: true,
})

// 标签输入
const tagInputVisible = ref(false)
const tagInputValue = ref('')

// 对话框状态
const showTableConfigDialog = ref(false)
const showDefectRuleDialog = ref(false)
const showButtonGroupDialog = ref(false)
const showStatusButtonDialog = ref(false)

// 缺陷规则表单
const defectRuleFormRef = ref<FormInstance>()
const defectRuleMode = ref<'create' | 'edit'>('create')
const defectRuleActiveTab = ref('basic')
const currentDefectRuleIndex = ref(-1)

interface DefectRuleFormData {
  name: string
  description: string
  enabled: boolean
  trigger: {
    status: string[]
  }
  template: {
    titleTemplate: string
    descriptionTemplate: string
  }
  options: {
    autoGenerate: boolean
    requireConfirmation: boolean
    batchGenerate: boolean
  }
}

const defectRuleFormData = ref<DefectRuleFormData>({
  name: '',
  description: '',
  enabled: true,
  trigger: {
    status: [],
  },
  template: {
    titleTemplate: '',
    descriptionTemplate: '',
  },
  options: {
    autoGenerate: false,
    requireConfirmation: true,
    batchGenerate: true,
  },
})

const defectRuleFormRules: FormRules = {
  name: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 1, max: 100, message: '规则名称长度在 1 到 100 个字符', trigger: 'blur' },
  ],
  description: [
    { required: true, message: '请输入规则描述', trigger: 'blur' },
  ],
  'trigger.status': [
    { required: true, message: '请选择触发状态', trigger: 'change' },
  ],
  'template.titleTemplate': [
    { required: true, message: '请输入标题模板', trigger: 'blur' },
  ],
  'template.descriptionTemplate': [
    { required: true, message: '请输入描述模板', trigger: 'blur' },
  ],
}

// 状态按钮组表单
const buttonGroupFormRef = ref<FormInstance>()
const buttonGroupMode = ref<'create' | 'edit'>('create')
const currentButtonGroupIndex = ref(-1)

interface ButtonGroupFormData {
  name: string
  description: string
  layout: 'horizontal' | 'vertical' | 'grid'
}

const buttonGroupFormData = ref<ButtonGroupFormData>({
  name: '',
  description: '',
  layout: 'horizontal',
})

const buttonGroupFormRules: FormRules = {
  name: [
    { required: true, message: '请输入按钮组名称', trigger: 'blur' },
    { min: 1, max: 50, message: '按钮组名称长度在 1 到 50 个字符', trigger: 'blur' },
  ],
  description: [
    { required: true, message: '请输入按钮组描述', trigger: 'blur' },
  ],
}

// 状态按钮表单
const statusButtonFormRef = ref<FormInstance>()
const statusButtonMode = ref<'create' | 'edit'>('create')
const statusButtonActiveTab = ref('basic')
const currentStatusButtonIndex = ref(-1)
const currentButtonGroupForButton = ref<StatusButtonGroup | null>(null)

interface StatusButtonFormData {
  label: string
  status: string
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  icon: string
  enabled: boolean
  order: number
  action: {
    apiEndpoint: string
    method: 'POST' | 'PUT' | 'PATCH'
    requireComment: boolean
    confirmMessage: string
    successMessage: string
    payloadTemplate: Record<string, any>
  }
  displayConditions: {
    currentStatus: string[]
  }
}

const statusButtonFormData = ref<StatusButtonFormData>({
  label: '',
  status: '',
  type: 'primary',
  icon: '',
  enabled: true,
  order: 1,
  action: {
    apiEndpoint: '',
    method: 'POST',
    requireComment: false,
    confirmMessage: '',
    successMessage: '',
    payloadTemplate: {},
  },
  displayConditions: {
    currentStatus: [],
  },
})

// 请求参数模板文本
const payloadTemplateText = ref('')

const statusButtonFormRules: FormRules = {
  label: [
    { required: true, message: '请输入按钮标签', trigger: 'blur' },
    { min: 1, max: 20, message: '按钮标签长度在 1 到 20 个字符', trigger: 'blur' },
  ],
  status: [
    { required: true, message: '请输入按钮状态', trigger: 'blur' },
  ],
  'action.apiEndpoint': [
    { required: true, message: '请输入API接口', trigger: 'blur' },
  ],
}

// Form rules
const basicInfoRules: FormRules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 1, max: 100, message: '模板名称长度在 1 到 100 个字符', trigger: 'blur' },
  ],
  type: [
    { required: true, message: '请选择模板类型', trigger: 'change' },
  ],
}

// Item form
const itemFormVisible = ref(false)
const itemFormMode = ref<'create' | 'edit'>('create')
const currentItemIndex = ref(-1)
const itemCategories = ref<string[]>([])

interface ItemFormData {
  content: string
  category: string
  required: boolean
  dynamicFields: Record<string, any>
}

const itemFormData = ref<ItemFormData>({
  content: '',
  category: '',
  required: false,
  dynamicFields: {},
})

const itemFormRules: FormRules = {
  content: [
    { required: true, message: '请输入检查项内容', trigger: 'blur' },
    { min: 1, max: 500, message: '检查项内容长度在 1 到 500 个字符', trigger: 'blur' },
  ],
}

// Batch add
const batchAddVisible = ref(false)
const batchAddText = ref('')

// Computed
const dialogTitle = computed(() => {
  return props.mode === 'create' ? '创建模板' : '编辑模板'
})

const visibleColumnsCount = computed(() => {
  return templateConfig.value.tableConfig?.columns.filter(c => c.visible).length || 0
})

const customColumnsCount = computed(() => {
  return templateConfig.value.tableConfig?.columns.filter(c => !c.fixed && c.visible).length || 0
})

const hasUnsavedChangesComputed = computed(() => {
  return hasUnsavedChanges(templateConfig.value, originalConfig.value)
})

// 进度步骤
const progressSteps = computed(() => [
  {
    key: 'basicInfo',
    label: '基本信息',
    required: true,
    completed: validation.value.basicInfo
  },
  {
    key: 'tableConfig',
    label: '表头',
    required: false,
    completed: !!templateConfig.value.tableConfig
  },
  {
    key: 'items',
    label: '检查项',
    required: true,
    completed: validation.value.items
  },
  {
    key: 'defectRules',
    label: '缺陷规则',
    required: false,
    completed: templateConfig.value.defectRules.length > 0
  },
  {
    key: 'statusButtons',
    label: '状态按钮',
    required: false,
    completed: templateConfig.value.statusButtons.length > 0
  }
])

const batchPreviewItems = computed(() => {
  if (!batchAddText.value.trim()) return []

  const lines = batchAddText.value.split('\n').filter(line => line.trim())
  return lines.map(line => {
    const trimmed = line.trim()
    let content = trimmed
    let category = ''
    let required = false

    // Check for required marker (*)
    if (trimmed.endsWith(' *')) {
      required = true
      content = trimmed.slice(0, -2).trim()
    }

    // Check for category [category]
    const categoryMatch = content.match(/^\[([^\]]+)\]\s*(.+)$/)
    if (categoryMatch) {
      category = categoryMatch[1].trim()
      content = categoryMatch[2].trim()
    }

    return {
      content,
      category,
      required,
      dynamicFields: {},
    }
  }).filter(item => item.content)
})

// Watch props
watch(
  () => props.visible,
  (visible) => {
    dialogVisible.value = visible
    if (visible) {
      initForm()
      loadTemplateTypes()
    }
  },
  { immediate: true }
)

watch(dialogVisible, (visible) => {
  emit('update:visible', visible)
})

// Watch template config changes for validation
watch(
  () => templateConfig.value,
  (newConfig) => {
    validation.value = validateTemplateConfig(newConfig)
  },
  { deep: true }
)

// 测试默认配置
console.log('DEFAULT_TABLE_CONFIG 测试:', DEFAULT_TABLE_CONFIG)
console.log('DEFAULT_TABLE_CONFIG.columns 长度:', DEFAULT_TABLE_CONFIG.columns?.length)

// Methods
const initForm = async () => {
  if (props.mode === 'edit' && props.template) {
    // 编辑模式：加载完整配置
    try {
      const configResponse = await loadTemplateConfig(props.template.id)
      templateConfig.value = {
        basicInfo: {
          id: props.template.id,
          name: props.template.name,
          type: props.template.type,
          version: props.template.version,
          description: props.template.description,
          category: props.template.category,
          tags: props.template.tags || [],
          isActive: props.template.isActive,
          createdTime: props.template.createdTime,
          updatedTime: props.template.updatedTime,
        },
        tableConfig: configResponse.tableConfig || null,
        items: props.template.items.map((item, index) => ({
          ...item,
          tempId: `item_${Date.now()}_${index}`,
          dynamicFields: {},
        })),
        defectRules: configResponse.defectRules || [],
        statusButtons: configResponse.statusButtons || [],
      }
    } catch (error) {
      console.error('Failed to load template config:', error)
      // 降级到基本模板信息
      templateConfig.value = createConfigFromTemplate(props.template)
    }
  } else {
    // 创建模式：使用默认配置
    templateConfig.value = { ...DEFAULT_TEMPLATE_CONFIG }
  }

  // 保存原始配置用于比较
  originalConfig.value = JSON.parse(JSON.stringify(templateConfig.value))

  // Extract categories from existing items
  const categories = new Set<string>()
  templateConfig.value.items.forEach(item => {
    if (item.category) {
      categories.add(item.category)
    }
  })
  itemCategories.value = Array.from(categories)
}

const loadTemplateTypes = async () => {
  try {
    templateTypes.value = await getTemplateTypes()
  } catch (error) {
    console.error('Failed to load template types:', error)
  }
}

const handleClose = async () => {
  // 检查是否有未保存的更改
  if (hasUnsavedChangesComputed.value) {
    try {
      await ElMessageBox.confirm(
        '您有未保存的更改，确定要关闭吗？',
        '确认关闭',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
    } catch (error) {
      return // 用户取消关闭
    }
  }

  dialogVisible.value = false
}

const validateAllTabs = async (): Promise<boolean> => {
  // 验证基本信息
  if (!basicInfoFormRef.value) return false

  const basicValid = await basicInfoFormRef.value.validate().catch(() => false)
  if (!basicValid) {
    activeTab.value = 'basicInfo'
    ElMessage.error('请完善基本信息')
    return false
  }

  // 验证检查项
  if (templateConfig.value.items.length === 0) {
    ElMessage.warning('请至少添加一个检查项')
    activeTab.value = 'items'
    return false
  }

  // 验证缺陷规则（如果有的话）
  for (const rule of templateConfig.value.defectRules) {
    if (!rule.name || !rule.description || !rule.template.titleTemplate) {
      ElMessage.error(`缺陷规则 "${rule.name || '未命名'}" 配置不完整`)
      activeTab.value = 'defectRules'
      return false
    }
  }

  // 验证状态按钮（如果有的话）
  for (const group of templateConfig.value.statusButtons) {
    if (!group.name || !group.description) {
      ElMessage.error(`按钮组 "${group.name || '未命名'}" 配置不完整`)
      activeTab.value = 'statusButtons'
      return false
    }

    for (const button of group.buttons) {
      if (!button.label || !button.status || !button.action.apiEndpoint) {
        ElMessage.error(`按钮组 "${group.name}" 中的按钮 "${button.label || '未命名'}" 配置不完整`)
        activeTab.value = 'statusButtons'
        return false
      }
    }
  }

  return true
}

const handleSave = async () => {
  try {
    // 验证所有标签页
    const isValid = await validateAllTabs()
    if (!isValid) return

    saving.value = true

    if (props.mode === 'create') {
      // 创建模式：先创建基本模板，再保存完整配置
      const basicTemplate = {
        name: templateConfig.value.basicInfo.name,
        type: templateConfig.value.basicInfo.type,
        items: templateConfig.value.items.map((item, index) => ({
          id: item.id || `item_${Date.now()}_${index}`,
          sequence: index + 1,
          content: item.content,
          required: item.required,
          category: item.category || '',
        })),
      }

      const createdTemplate = await createTemplate(basicTemplate)

      // 保存完整配置
      if (createdTemplate.id) {
        await saveTemplateConfig(createdTemplate.id, {
          basicInfo: templateConfig.value.basicInfo,
          tableConfig: templateConfig.value.tableConfig,
          items: templateConfig.value.items,
          defectRules: templateConfig.value.defectRules,
          statusButtons: templateConfig.value.statusButtons,
        })
      }

      ElMessage.success('模板创建成功')
    } else if (props.template) {
      // 编辑模式：保存完整配置
      await saveTemplateConfig(props.template.id, {
        basicInfo: templateConfig.value.basicInfo,
        tableConfig: templateConfig.value.tableConfig,
        items: templateConfig.value.items,
        defectRules: templateConfig.value.defectRules,
        statusButtons: templateConfig.value.statusButtons,
      })

      ElMessage.success('模板配置保存成功')
    }

    emit('success')
  } catch (error) {
    console.error('Failed to save template:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

const handleAddItem = () => {
  console.log('添加检查项，当前表头配置:', templateConfig.value.tableConfig)
  console.log('customColumnsCount:', customColumnsCount.value)

  // 初始化动态字段
  const dynamicFields: Record<string, any> = {}
  if (templateConfig.value.tableConfig) {
    const customColumns = templateConfig.value.tableConfig.columns.filter(c => !c.fixed && c.visible)
    console.log('自定义列:', customColumns.map(c => ({ label: c.label, key: c.key })))

    customColumns.forEach(column => {
      dynamicFields[column.key] = ''
    })
  }

  console.log('初始化的动态字段:', dynamicFields)

  itemFormData.value = {
    content: '',
    category: '',
    required: false,
    dynamicFields,
  }
  itemFormMode.value = 'create'
  currentItemIndex.value = -1
  itemFormVisible.value = true
}

const handleEditItem = (index: number) => {
  const item = templateConfig.value.items[index]

  // 确保动态字段包含所有当前表头配置的字段
  const dynamicFields: Record<string, any> = { ...item.dynamicFields }
  if (templateConfig.value.tableConfig) {
    templateConfig.value.tableConfig.columns
      .filter(c => !c.fixed && c.visible)
      .forEach(column => {
        if (!(column.key in dynamicFields)) {
          dynamicFields[column.key] = ''
        }
      })
  }

  itemFormData.value = {
    content: item.content,
    category: item.category || '',
    required: item.required,
    dynamicFields,
  }
  itemFormMode.value = 'edit'
  currentItemIndex.value = index
  itemFormVisible.value = true
}

const handleDeleteItem = async (index: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个检查项吗？',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    templateConfig.value.items.splice(index, 1)
    ElMessage.success('检查项删除成功')
  } catch (error) {
    // User cancelled
  }
}

const handleItemFormClose = () => {
  itemFormVisible.value = false
}

const handleItemSave = async () => {
  if (!itemFormRef.value) return

  try {
    const valid = await itemFormRef.value.validate()
    if (!valid) return

    const newItem: ExtendedChecklistItem = {
      id: itemFormMode.value === 'create' ? `item_${Date.now()}_${Math.random()}` : templateConfig.value.items[currentItemIndex.value].id,
      tempId: `item_${Date.now()}_${Math.random()}`,
      sequence: 0, // Will be updated when saving
      content: itemFormData.value.content,
      category: itemFormData.value.category,
      required: itemFormData.value.required,
      dynamicFields: itemFormData.value.dynamicFields,
    }

    if (itemFormMode.value === 'create') {
      templateConfig.value.items.push(newItem)
      ElMessage.success('检查项添加成功')
    } else {
      templateConfig.value.items[currentItemIndex.value] = newItem
      ElMessage.success('检查项更新成功')
    }

    // Update categories
    if (newItem.category && !itemCategories.value.includes(newItem.category)) {
      itemCategories.value.push(newItem.category)
    }

    itemFormVisible.value = false
  } catch (error) {
    console.error('Failed to save item:', error)
  }
}

const handleBatchAdd = () => {
  batchAddText.value = ''
  batchAddVisible.value = true
}

const handleBatchAddClose = () => {
  batchAddVisible.value = false
}

const handleBatchAddConfirm = () => {
  const newItems: ExtendedChecklistItem[] = batchPreviewItems.value.map((item, index) => ({
    id: `item_${Date.now()}_${index}`,
    tempId: `item_${Date.now()}_${index}`,
    sequence: 0,
    content: item.content,
    category: item.category,
    required: item.required,
    dynamicFields: item.dynamicFields,
  }))

  templateConfig.value.items.push(...newItems)

  // Update categories
  newItems.forEach(item => {
    if (item.category && !itemCategories.value.includes(item.category)) {
      itemCategories.value.push(item.category)
    }
  })

  ElMessage.success(`成功添加 ${newItems.length} 个检查项`)
  batchAddVisible.value = false
}

const handleDragEnd = () => {
  // Items are automatically reordered by draggable
  ElMessage.success('检查项顺序已更新')
}

// 标签页相关方法
const canAccessTab = (tabName: string): boolean => {
  switch (tabName) {
    case 'basicInfo':
      return true // 基本信息总是可以访问
    case 'tableConfig':
      return validation.value.basicInfo // 需要完成基本信息
    case 'items':
      return validation.value.basicInfo // 需要完成基本信息
    case 'defectRules':
      return validation.value.basicInfo && validation.value.items // 需要完成基本信息和检查项
    case 'statusButtons':
      return validation.value.basicInfo && validation.value.items // 需要完成基本信息和检查项
    default:
      return false
  }
}

const handleTabChange = (tabName: string) => {
  if (canAccessTab(tabName)) {
    activeTab.value = tabName
  } else {
    // 显示提示信息
    const requirements = getTabRequirements(tabName)
    ElMessage.warning(`请先完成：${requirements}`)
  }
}

const getTabRequirements = (tabName: string): string => {
  switch (tabName) {
    case 'tableConfig':
    case 'items':
      return '基本信息'
    case 'defectRules':
    case 'statusButtons':
      return '基本信息和检查项配置'
    default:
      return ''
  }
}

// 标签相关方法
const showTagInput = () => {
  tagInputVisible.value = true
  nextTick(() => {
    tagInputRef.value?.focus()
  })
}

const addTag = () => {
  const tag = tagInputValue.value.trim()
  if (tag && !templateConfig.value.basicInfo.tags?.includes(tag)) {
    if (!templateConfig.value.basicInfo.tags) {
      templateConfig.value.basicInfo.tags = []
    }
    templateConfig.value.basicInfo.tags.push(tag)
  }
  tagInputValue.value = ''
  tagInputVisible.value = false
}

const removeTag = (tag: string) => {
  const tags = templateConfig.value.basicInfo.tags
  if (tags) {
    const index = tags.indexOf(tag)
    if (index > -1) {
      tags.splice(index, 1)
    }
  }
}

// 表头配置相关方法
const openTableConfigDialog = () => {
  const currentConfig = templateConfig.value.tableConfig || DEFAULT_TABLE_CONFIG
  console.log('打开表头配置对话框，当前配置:', currentConfig)
  console.log('默认配置:', DEFAULT_TABLE_CONFIG)
  console.log('templateConfig.tableConfig:', templateConfig.value.tableConfig)
  console.log('传递给TableColumnConfig的配置:', currentConfig)
  console.log('配置的columns数量:', currentConfig.columns?.length)
  showTableConfigDialog.value = true
}

const handleTableConfigChange = (config: TableViewConfig) => {
  console.log('收到表头配置更新:', config)
  templateConfig.value.tableConfig = config

  // 调试信息：显示自定义列数量
  const customCols = config.columns.filter(c => !c.fixed && c.visible)
  console.log('表头配置已更新，自定义列数量:', customCols.length, '自定义列:', customCols.map(c => ({ label: c.label, key: c.key, fixed: c.fixed, visible: c.visible })))
  console.log('当前 templateConfig.tableConfig:', templateConfig.value.tableConfig)
  console.log('当前 customColumnsCount:', customColumnsCount.value)

  ElMessage.success(`表头配置已更新，包含 ${customCols.length} 个自定义字段`)
}

const handleTableConfigClose = () => {
  showTableConfigDialog.value = false
}

const getDynamicFieldLabel = (key: string): string => {
  const column = templateConfig.value.tableConfig?.columns.find(c => c.key === key)
  return column?.label || key
}

// 缺陷规则相关方法
const handleAddDefectRule = () => {
  defectRuleFormData.value = {
    name: '',
    description: '',
    enabled: true,
    trigger: {
      status: [],
    },
    template: {
      titleTemplate: '{category} - {content}',
      descriptionTemplate: '检查项：{content}\n分类：{category}\n评审人：{reviewer}\n状态：不通过',
    },
    options: {
      autoGenerate: false,
      requireConfirmation: true,
      batchGenerate: true,
    },
  }
  defectRuleMode.value = 'create'
  defectRuleActiveTab.value = 'basic'
  currentDefectRuleIndex.value = -1
  showDefectRuleDialog.value = true
}

const handleRuleToggle = (rule: DefectRule) => {
  ElMessage.success(`规则 "${rule.name}" 已${rule.enabled ? '启用' : '禁用'}`)
}

const editDefectRule = (rule: DefectRule) => {
  const index = templateConfig.value.defectRules.findIndex(r => r.id === rule.id)
  if (index === -1) return

  defectRuleFormData.value = {
    name: rule.name,
    description: rule.description,
    enabled: rule.enabled,
    trigger: {
      status: [...rule.trigger.status],
    },
    template: {
      titleTemplate: rule.template.titleTemplate,
      descriptionTemplate: rule.template.descriptionTemplate,
    },
    options: {
      autoGenerate: rule.options.autoGenerate,
      requireConfirmation: rule.options.requireConfirmation,
      batchGenerate: rule.options.batchGenerate,
    },
  }
  defectRuleMode.value = 'edit'
  defectRuleActiveTab.value = 'basic'
  currentDefectRuleIndex.value = index
  showDefectRuleDialog.value = true
}

const duplicateDefectRule = async (rule: DefectRule) => {
  try {
    const { value: newName } = await ElMessageBox.prompt(
      '请输入新规则的名称',
      '复制缺陷规则',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: `${rule.name} - 副本`,
        inputValidator: (value) => {
          if (!value || !value.trim()) {
            return '规则名称不能为空'
          }
          if (templateConfig.value.defectRules.some((r) => r.name === value.trim())) {
            return '规则名称已存在'
          }
          return true
        },
      }
    )

    const newRule: DefectRule = {
      ...rule,
      id: `rule_${Date.now()}`,
      name: newName.trim(),
    }

    templateConfig.value.defectRules.push(newRule)
    ElMessage.success('缺陷规则复制成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to duplicate defect rule:', error)
    }
  }
}

const deleteDefectRule = async (ruleId: string) => {
  try {
    const rule = templateConfig.value.defectRules.find(r => r.id === ruleId)
    if (!rule) return

    await ElMessageBox.confirm(
      `确定要删除规则 "${rule.name}" 吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const index = templateConfig.value.defectRules.findIndex(r => r.id === ruleId)
    if (index > -1) {
      templateConfig.value.defectRules.splice(index, 1)
      ElMessage.success('缺陷规则删除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete defect rule:', error)
    }
  }
}

const handleDefectRuleClose = () => {
  showDefectRuleDialog.value = false
}

const handleDefectRuleSave = async () => {
  if (!defectRuleFormRef.value) return

  try {
    const valid = await defectRuleFormRef.value.validate()
    if (!valid) return

    const newRule: DefectRule = {
      id: defectRuleMode.value === 'create' ? `rule_${Date.now()}` : templateConfig.value.defectRules[currentDefectRuleIndex.value].id,
      name: defectRuleFormData.value.name,
      description: defectRuleFormData.value.description,
      enabled: defectRuleFormData.value.enabled,
      trigger: {
        status: defectRuleFormData.value.trigger.status as any[],
        conditions: [],
      },
      template: {
        titleTemplate: defectRuleFormData.value.template.titleTemplate,
        descriptionTemplate: defectRuleFormData.value.template.descriptionTemplate,
        severityMapping: {},
        categoryMapping: {},
        customFieldMapping: {},
      },
      options: defectRuleFormData.value.options,
    }

    if (defectRuleMode.value === 'create') {
      templateConfig.value.defectRules.push(newRule)
      ElMessage.success('缺陷规则添加成功')
    } else {
      templateConfig.value.defectRules[currentDefectRuleIndex.value] = newRule
      ElMessage.success('缺陷规则更新成功')
    }

    showDefectRuleDialog.value = false
  } catch (error) {
    console.error('Failed to save defect rule:', error)
  }
}

const getStatusText = (status: any): string => {
  const statusMap: Record<string, string> = {
    [ReviewItemStatus.PENDING]: '待处理',
    [ReviewItemStatus.PASS]: '通过',
    [ReviewItemStatus.FAIL]: '不通过',
    [ReviewItemStatus.SKIP]: '跳过',
  }
  return statusMap[status] || status
}

// 状态按钮相关方法
const handleAddButtonGroup = () => {
  console.log('点击添加按钮组')
  buttonGroupFormData.value = {
    name: '',
    description: '',
    layout: 'horizontal',
  }
  buttonGroupMode.value = 'create'
  currentButtonGroupIndex.value = -1
  showButtonGroupDialog.value = true
  console.log('按钮组对话框应该显示:', showButtonGroupDialog.value)
}

const editButtonGroup = (group: StatusButtonGroup) => {
  const index = templateConfig.value.statusButtons.findIndex(g => g.id === group.id)
  if (index === -1) return

  buttonGroupFormData.value = {
    name: group.name,
    description: group.description,
    layout: group.layout,
  }
  buttonGroupMode.value = 'edit'
  currentButtonGroupIndex.value = index
  showButtonGroupDialog.value = true
}

const duplicateButtonGroup = async (group: StatusButtonGroup) => {
  try {
    const { value: newName } = await ElMessageBox.prompt(
      '请输入新按钮组的名称',
      '复制按钮组',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: `${group.name} - 副本`,
        inputValidator: (value) => {
          if (!value || !value.trim()) {
            return '按钮组名称不能为空'
          }
          if (templateConfig.value.statusButtons.some((g) => g.name === value.trim())) {
            return '按钮组名称已存在'
          }
          return true
        },
      }
    )

    const newGroup: StatusButtonGroup = {
      ...group,
      id: `group_${Date.now()}`,
      name: newName.trim(),
      buttons: group.buttons.map(btn => ({
        ...btn,
        id: `btn_${Date.now()}_${Math.random()}`,
      })),
    }

    templateConfig.value.statusButtons.push(newGroup)
    ElMessage.success('按钮组复制成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to duplicate button group:', error)
    }
  }
}

const deleteButtonGroup = async (groupId: string) => {
  try {
    const group = templateConfig.value.statusButtons.find(g => g.id === groupId)
    if (!group) return

    await ElMessageBox.confirm(
      `确定要删除按钮组 "${group.name}" 吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const index = templateConfig.value.statusButtons.findIndex(g => g.id === groupId)
    if (index > -1) {
      templateConfig.value.statusButtons.splice(index, 1)
      ElMessage.success('按钮组删除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete button group:', error)
    }
  }
}

const handleButtonGroupClose = () => {
  showButtonGroupDialog.value = false
}

const handleButtonGroupSave = async () => {
  if (!buttonGroupFormRef.value) return

  try {
    const valid = await buttonGroupFormRef.value.validate()
    if (!valid) return

    const newGroup: StatusButtonGroup = {
      id: buttonGroupMode.value === 'create' ? `group_${Date.now()}` : templateConfig.value.statusButtons[currentButtonGroupIndex.value].id,
      name: buttonGroupFormData.value.name,
      description: buttonGroupFormData.value.description,
      layout: buttonGroupFormData.value.layout,
      buttons: buttonGroupMode.value === 'edit' ? templateConfig.value.statusButtons[currentButtonGroupIndex.value].buttons : [],
      defaultButtons: [],
    }

    if (buttonGroupMode.value === 'create') {
      templateConfig.value.statusButtons.push(newGroup)
      ElMessage.success('按钮组添加成功')
    } else {
      templateConfig.value.statusButtons[currentButtonGroupIndex.value] = newGroup
      ElMessage.success('按钮组更新成功')
    }

    showButtonGroupDialog.value = false
  } catch (error) {
    console.error('Failed to save button group:', error)
  }
}

// 状态按钮方法
const handleAddStatusButton = (group: StatusButtonGroup) => {
  statusButtonFormData.value = {
    label: '',
    status: '',
    type: 'primary',
    icon: '',
    enabled: true,
    order: group.buttons.length + 1,
    action: {
      apiEndpoint: '',
      method: 'POST',
      requireComment: false,
      confirmMessage: '',
      successMessage: '',
      payloadTemplate: {},
    },
    displayConditions: {
      currentStatus: [],
    },
  }
  payloadTemplateText.value = ''
  statusButtonMode.value = 'create'
  statusButtonActiveTab.value = 'basic'
  currentStatusButtonIndex.value = -1
  currentButtonGroupForButton.value = group
  showStatusButtonDialog.value = true
}

const editStatusButton = (group: StatusButtonGroup, button: any) => {
  const index = group.buttons.findIndex(b => b.id === button.id)
  if (index === -1) return

  statusButtonFormData.value = {
    label: button.label,
    status: button.status,
    type: button.type,
    icon: button.icon || '',
    enabled: button.enabled,
    order: button.order,
    action: {
      apiEndpoint: button.action.apiEndpoint,
      method: button.action.method,
      requireComment: button.action.requireComment,
      confirmMessage: button.action.confirmMessage || '',
      successMessage: button.action.successMessage || '',
      payloadTemplate: button.action.payloadTemplate || {},
    },
    displayConditions: {
      currentStatus: button.displayConditions?.currentStatus || [],
    },
  }

  payloadTemplateText.value = JSON.stringify(button.action.payloadTemplate || {}, null, 2)
  statusButtonMode.value = 'edit'
  statusButtonActiveTab.value = 'basic'
  currentStatusButtonIndex.value = index
  currentButtonGroupForButton.value = group
  showStatusButtonDialog.value = true
}

const deleteStatusButton = async (group: StatusButtonGroup, buttonId: string) => {
  try {
    const button = group.buttons.find(b => b.id === buttonId)
    if (!button) return

    await ElMessageBox.confirm(
      `确定要删除按钮 "${button.label}" 吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const index = group.buttons.findIndex(b => b.id === buttonId)
    if (index > -1) {
      group.buttons.splice(index, 1)
      ElMessage.success('状态按钮删除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete status button:', error)
    }
  }
}

const handleStatusButtonClose = () => {
  showStatusButtonDialog.value = false
}

const handleStatusButtonSave = async () => {
  if (!statusButtonFormRef.value || !currentButtonGroupForButton.value) return

  try {
    const valid = await statusButtonFormRef.value.validate()
    if (!valid) return

    // 解析请求参数模板
    let payloadTemplate = {}
    if (payloadTemplateText.value.trim()) {
      try {
        payloadTemplate = JSON.parse(payloadTemplateText.value)
      } catch (error) {
        ElMessage.error('请求参数模板格式错误，请检查JSON格式')
        return
      }
    }

    const newButton = {
      id: statusButtonMode.value === 'create' ? `btn_${Date.now()}` : currentButtonGroupForButton.value.buttons[currentStatusButtonIndex.value].id,
      label: statusButtonFormData.value.label,
      status: statusButtonFormData.value.status,
      type: statusButtonFormData.value.type,
      icon: statusButtonFormData.value.icon || undefined,
      enabled: statusButtonFormData.value.enabled,
      order: statusButtonFormData.value.order,
      action: {
        ...statusButtonFormData.value.action,
        payloadTemplate,
      },
      displayConditions: {
        currentStatus: statusButtonFormData.value.displayConditions.currentStatus,
      },
    }

    if (statusButtonMode.value === 'create') {
      currentButtonGroupForButton.value.buttons.push(newButton)
      ElMessage.success('状态按钮添加成功')
    } else {
      currentButtonGroupForButton.value.buttons[currentStatusButtonIndex.value] = newButton
      ElMessage.success('状态按钮更新成功')
    }

    showStatusButtonDialog.value = false
  } catch (error) {
    console.error('Failed to save status button:', error)
  }
}

const handleButtonDragEnd = () => {
  ElMessage.success('按钮顺序已更新')
}
</script>

<style scoped>
.form-section {
  margin-bottom: 20px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-weight: 600;
  color: #303133;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.section-actions {
  display: flex;
  gap: 8px;
}

.items-container {
  max-height: 400px;
  overflow-y: auto;
}

.item-card {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  background: #fafafa;
  transition: all 0.2s;
}

.item-card:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.item-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.drag-handle {
  cursor: move;
  color: #909399;
}

.item-sequence {
  font-weight: 600;
  color: #606266;
  min-width: 20px;
}

.item-actions {
  display: flex;
  gap: 4px;
}

.item-content {
  color: #303133;
  line-height: 1.5;
  padding-left: 28px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.batch-add-content {
  max-height: 60vh;
  overflow-y: auto;
}

.batch-preview {
  margin-top: 16px;
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

.batch-preview h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.preview-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
  background: #fafafa;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  font-size: 14px;
}

.preview-sequence {
  color: #909399;
  min-width: 20px;
}

.preview-content {
  flex: 1;
  color: #303133;
}

:deep(.el-card__header) {
  padding: 12px 16px;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

/* 新增样式 */
.template-form-dialog {
  .el-dialog__body {
    padding: 0;
  }
}

.template-tabs {
  .el-tabs__content {
    padding: 20px;
  }
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
  position: relative;
}

.tab-label.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.lock-icon {
  color: #909399;
  font-size: 12px;
}

/* 进度指示器样式 */
.progress-indicator {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
  min-width: 0;
}

.progress-steps::before {
  content: '';
  position: absolute;
  top: 16px;
  left: 20px;
  right: 20px;
  height: 2px;
  background: #e4e7ed;
  z-index: 1;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  background: #f8f9fa;
  padding: 0 4px;
  flex: 1;
  min-width: 0;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 6px;
  border: 2px solid #e4e7ed;
  background: #fff;
  color: #909399;
  transition: all 0.3s;
}

.step-label {
  font-size: 12px;
  color: #606266;
  text-align: center;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px;
}

.step-required {
  color: #f56c6c;
  font-size: 12px;
  position: absolute;
  top: -2px;
  right: -8px;
}

.progress-step.completed .step-number {
  background: #67c23a;
  border-color: #67c23a;
  color: #fff;
}

.progress-step.completed.required .step-number {
  background: #f56c6c;
  border-color: #f56c6c;
  color: #fff;
}

.progress-step.active .step-number {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}

.progress-step.disabled {
  opacity: 0.5;
}

.progress-step.disabled .step-label {
  color: #c0c4cc;
}

/* 表单行样式 */
.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 0;
}

.form-item-half {
  flex: 1;
  min-width: 0;
}

/* 状态按钮预览样式 */
.buttons-preview {
  margin-top: 12px;
}

.buttons-draggable {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.button-preview-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: #f8f9fa;
  transition: all 0.3s;
}

.button-preview-item:hover {
  border-color: #409eff;
  background: #ecf5ff;
}

.button-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.drag-handle {
  cursor: move;
  color: #909399;
  font-size: 14px;
}

.drag-handle:hover {
  color: #409eff;
}

.button-actions {
  display: flex;
  gap: 4px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.tab-form {
  max-height: 50vh;
  overflow-y: auto;
  padding-right: 8px;
}

.tab-content {
  max-height: 50vh;
  overflow-y: auto;
  padding-right: 8px;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.header-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.header-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.config-preview {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.config-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-item .label {
  color: #606266;
  font-size: 14px;
}

.summary-item .value {
  color: #303133;
  font-weight: 500;
}

.rules-list,
.button-groups-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.rule-item,
.group-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background: #fff;
  transition: all 0.2s;
}

.rule-item:hover,
.group-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.rule-item.disabled {
  opacity: 0.6;
  background: #f5f7fa;
}

.rule-header,
.group-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.rule-info,
.group-info {
  flex: 1;
}

.rule-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.rule-title h5,
.group-info h5 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.rule-description,
.group-info p {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 14px;
}

.rule-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.rule-actions,
.group-actions {
  display: flex;
  gap: 8px;
}

.buttons-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.more-buttons {
  color: #909399;
  font-size: 12px;
}

.dynamic-fields {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.dynamic-fields h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.form-tip {
  margin-top: 4px;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}
</style>