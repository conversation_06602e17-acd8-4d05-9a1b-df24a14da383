<template>
  <div class="button-config-embedded">
    <div class="config-header">
      <div class="header-info">
        <h4>自定义按钮配置</h4>
        <p>配置评审界面的自定义操作按钮</p>
      </div>
      <div class="header-actions">
        <el-button size="small" @click="openAdvancedConfig">
          <el-icon><Link /></el-icon>
          高级配置
        </el-button>
        <el-button size="small" type="primary" @click="showAddGroupDialog = true">
          <el-icon><Plus /></el-icon>
          添加按钮组
        </el-button>
      </div>
    </div>

    <!-- 功能说明 -->
    <el-alert
      title="功能说明"
      type="info"
      :closable="false"
      style="margin-bottom: 16px"
    >
      <p>这里提供基本的按钮组管理功能。如需配置复杂的API参数、显示条件、后续动作或按钮排序，请点击"高级配置"。</p>
    </el-alert>

    <!-- 按钮组列表 -->
    <div class="button-groups-list">
      <div v-if="buttonGroups.length === 0" class="empty-state">
        <el-empty description="暂无自定义按钮组" :image-size="60">
          <el-button type="primary" @click="showAddGroupDialog = true">
            添加第一个按钮组
          </el-button>
        </el-empty>
      </div>
      
      <div v-else class="groups-grid">
        <div
          v-for="(group, groupIndex) in buttonGroups"
          :key="group.id"
          class="group-card"
        >
          <div class="group-header">
            <div class="group-info">
              <h5>{{ group.name }}</h5>
              <p>{{ group.description }}</p>
            </div>
            <div class="group-actions">
              <el-button size="small" text @click="editGroup(group, groupIndex)">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button size="small" text type="danger" @click="deleteGroup(groupIndex)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
          
          <!-- 按钮预览 -->
          <div class="buttons-preview">
            <div class="buttons-list">
              <el-button
                v-for="button in group.buttons"
                :key="button.id"
                :type="button.type"
                size="small"
                disabled
              >
                <el-icon v-if="button.icon">
                  <component :is="button.icon" />
                </el-icon>
                {{ button.label }}
              </el-button>
            </div>
            <el-button
              size="small"
              text
              type="primary"
              @click="addButtonToGroup(group, groupIndex)"
            >
              <el-icon><Plus /></el-icon>
              添加按钮
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑按钮组对话框 -->
    <el-dialog
      v-model="showAddGroupDialog"
      :title="editingGroupIndex >= 0 ? '编辑按钮组' : '添加按钮组'"
      width="500px"
    >
      <el-form :model="groupForm" :rules="groupRules" ref="groupFormRef" label-width="100px">
        <el-form-item label="组名称" prop="name">
          <el-input v-model="groupForm.name" placeholder="请输入按钮组名称" />
        </el-form-item>
        <el-form-item label="组描述" prop="description">
          <el-input
            v-model="groupForm.description"
            type="textarea"
            :rows="2"
            placeholder="请输入按钮组描述"
          />
        </el-form-item>
        <el-form-item label="布局方式">
          <el-radio-group v-model="groupForm.layout">
            <el-radio label="horizontal">水平排列</el-radio>
            <el-radio label="vertical">垂直排列</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="cancelGroupEdit">取消</el-button>
        <el-button type="primary" @click="saveGroup">保存</el-button>
      </template>
    </el-dialog>

    <!-- 添加按钮对话框 -->
    <el-dialog
      v-model="showAddButtonDialog"
      title="添加按钮"
      width="500px"
    >
      <el-form :model="buttonForm" :rules="buttonRules" ref="buttonFormRef" label-width="100px">
        <el-form-item label="按钮标签" prop="label">
          <el-input v-model="buttonForm.label" placeholder="请输入按钮标签" />
        </el-form-item>
        <el-form-item label="按钮类型" prop="type">
          <el-select v-model="buttonForm.type" placeholder="选择按钮类型">
            <el-option label="主要" value="primary" />
            <el-option label="成功" value="success" />
            <el-option label="警告" value="warning" />
            <el-option label="危险" value="danger" />
            <el-option label="信息" value="info" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态值" prop="status">
          <el-select v-model="buttonForm.status" placeholder="选择状态值" allow-create filterable>
            <el-option label="通过" value="PASS" />
            <el-option label="不通过" value="FAIL" />
            <el-option label="条件通过" value="CONDITIONAL_PASS" />
            <el-option label="待评审" value="PENDING" />
          </el-select>
        </el-form-item>
        <el-form-item label="API接口" prop="apiEndpoint">
          <el-input
            v-model="buttonForm.apiEndpoint"
            placeholder="例如：/api/review/items/{itemId}/status"
          />
        </el-form-item>
        <el-form-item label="需要备注">
          <el-switch v-model="buttonForm.requireComment" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="cancelButtonEdit">取消</el-button>
        <el-button type="primary" @click="saveButton">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Plus, Edit, Delete, Setting, Link } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { type StatusButtonGroup, type CustomStatusButton } from '@/types/defect-config'

// Props
interface Props {
  modelValue: StatusButtonGroup[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [groups: StatusButtonGroup[]]
}>()

// Reactive data
const buttonGroups = ref<StatusButtonGroup[]>([])
const showAddGroupDialog = ref(false)
const showAddButtonDialog = ref(false)
const editingGroupIndex = ref(-1)
const currentGroupIndex = ref(-1)
const groupFormRef = ref<FormInstance>()
const buttonFormRef = ref<FormInstance>()

// Form data
const groupForm = ref({
  name: '',
  description: '',
  layout: 'horizontal' as 'horizontal' | 'vertical'
})

const buttonForm = ref({
  label: '',
  type: 'primary' as 'primary' | 'success' | 'warning' | 'danger' | 'info',
  status: '',
  apiEndpoint: '',
  requireComment: false
})

// Form rules
const groupRules: FormRules = {
  name: [{ required: true, message: '请输入按钮组名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入按钮组描述', trigger: 'blur' }]
}

const buttonRules: FormRules = {
  label: [{ required: true, message: '请输入按钮标签', trigger: 'blur' }],
  type: [{ required: true, message: '请选择按钮类型', trigger: 'change' }],
  status: [{ required: true, message: '请输入状态值', trigger: 'blur' }],
  apiEndpoint: [{ required: true, message: '请输入API接口', trigger: 'blur' }]
}

// Watch props
watch(
  () => props.modelValue,
  (newGroups) => {
    buttonGroups.value = [...newGroups]
  },
  { immediate: true }
)

// Watch buttonGroups and emit changes
watch(
  buttonGroups,
  (newGroups) => {
    emit('update:modelValue', [...newGroups])
  },
  { deep: true }
)

// Methods
const editGroup = (group: StatusButtonGroup, index: number) => {
  editingGroupIndex.value = index
  groupForm.value = {
    name: group.name,
    description: group.description,
    layout: group.layout
  }
  showAddGroupDialog.value = true
}

const deleteGroup = async (index: number) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除按钮组 "${buttonGroups.value[index].name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    buttonGroups.value.splice(index, 1)
    ElMessage.success('按钮组删除成功')
  } catch (error) {
    // User cancelled
  }
}

const addButtonToGroup = (group: StatusButtonGroup, groupIndex: number) => {
  currentGroupIndex.value = groupIndex
  buttonForm.value = {
    label: '',
    type: 'primary',
    status: '',
    apiEndpoint: '',
    requireComment: false
  }
  showAddButtonDialog.value = true
}

const saveGroup = async () => {
  if (!groupFormRef.value) return
  
  try {
    const valid = await groupFormRef.value.validate()
    if (!valid) return
    
    const groupData: StatusButtonGroup = {
      id: editingGroupIndex.value >= 0 ? buttonGroups.value[editingGroupIndex.value].id : `group_${Date.now()}`,
      name: groupForm.value.name,
      description: groupForm.value.description,
      layout: groupForm.value.layout,
      buttons: editingGroupIndex.value >= 0 ? buttonGroups.value[editingGroupIndex.value].buttons : [],
      defaultButtons: []
    }
    
    if (editingGroupIndex.value >= 0) {
      buttonGroups.value[editingGroupIndex.value] = groupData
      ElMessage.success('按钮组更新成功')
    } else {
      buttonGroups.value.push(groupData)
      ElMessage.success('按钮组添加成功')
    }
    
    showAddGroupDialog.value = false
    resetGroupForm()
  } catch (error) {
    // Validation failed
  }
}

const saveButton = async () => {
  if (!buttonFormRef.value) return
  
  try {
    const valid = await buttonFormRef.value.validate()
    if (!valid) return
    
    const buttonData: CustomStatusButton = {
      id: `btn_${Date.now()}`,
      label: buttonForm.value.label,
      status: buttonForm.value.status,
      type: buttonForm.value.type,
      enabled: true,
      order: buttonGroups.value[currentGroupIndex.value].buttons.length + 1,
      action: {
        apiEndpoint: buttonForm.value.apiEndpoint,
        method: 'PUT',
        requireComment: buttonForm.value.requireComment,
        payloadTemplate: {
          status: buttonForm.value.status
        }
      }
    }
    
    buttonGroups.value[currentGroupIndex.value].buttons.push(buttonData)
    ElMessage.success('按钮添加成功')
    
    showAddButtonDialog.value = false
    resetButtonForm()
  } catch (error) {
    // Validation failed
  }
}

const cancelGroupEdit = () => {
  showAddGroupDialog.value = false
  resetGroupForm()
}

const cancelButtonEdit = () => {
  showAddButtonDialog.value = false
  resetButtonForm()
}

const resetGroupForm = () => {
  editingGroupIndex.value = -1
  groupForm.value = {
    name: '',
    description: '',
    layout: 'horizontal'
  }
}

const openAdvancedConfig = () => {
  const url = '/admin/button-config'
  window.open(url, '_blank')
  ElMessage.info('已在新窗口打开高级配置页面')
}

const resetButtonForm = () => {
  currentGroupIndex.value = -1
  buttonForm.value = {
    label: '',
    type: 'primary',
    status: '',
    apiEndpoint: '',
    requireComment: false
  }
}
</script>

<style scoped>
.button-config-embedded {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.header-info h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-weight: 600;
}

.header-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.groups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 12px;
}

.group-card {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  transition: all 0.2s;
}

.group-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.group-info h5 {
  margin: 0 0 4px 0;
  color: #303133;
  font-weight: 600;
}

.group-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.group-actions {
  display: flex;
  gap: 4px;
}

.buttons-preview {
  border-top: 1px solid #e4e7ed;
  padding-top: 12px;
}

.buttons-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}
</style>
