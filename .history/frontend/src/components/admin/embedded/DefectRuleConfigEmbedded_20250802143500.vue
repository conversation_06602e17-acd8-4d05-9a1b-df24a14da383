<template>
  <div class="defect-rule-config-embedded">
    <div class="config-header">
      <div class="header-info">
        <h4>缺陷生成规则</h4>
        <p>配置检查项不通过时自动生成缺陷的规则</p>
      </div>
      <div class="header-actions">
        <el-button size="small" @click="openAdvancedConfig">
          <el-icon><Setting /></el-icon>
          高级配置
        </el-button>
        <el-button size="small" type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          添加规则
        </el-button>
      </div>
    </div>

    <!-- 功能说明 -->
    <el-alert
      title="基本配置 vs 高级配置"
      type="info"
      :closable="false"
      style="margin-bottom: 16px"
    >
      <div class="feature-comparison">
        <div class="basic-features">
          <strong>当前可配置：</strong>
          <ul>
            <li>规则名称和描述</li>
            <li>触发状态选择</li>
            <li>基本模板配置</li>
            <li>启用/禁用规则</li>
          </ul>
        </div>
        <div class="advanced-features">
          <strong>高级配置包含：</strong>
          <ul>
            <li>复杂触发条件配置</li>
            <li>模板变量和映射规则</li>
            <li>严重程度和分类映射</li>
            <li>规则测试和验证</li>
            <li>批量操作和导入导出</li>
          </ul>
        </div>
      </div>
    </el-alert>

    <!-- 规则列表 -->
    <div class="rules-list">
      <div v-if="rules.length === 0" class="empty-state">
        <el-empty description="暂无缺陷生成规则" :image-size="60">
          <el-button type="primary" @click="showAddDialog = true">
            添加第一个规则
          </el-button>
        </el-empty>
      </div>
      
      <div v-else class="rules-grid">
        <div
          v-for="(rule, index) in rules"
          :key="rule.id"
          class="rule-card"
        >
          <div class="rule-header">
            <div class="rule-title">
              <span>{{ rule.name }}</span>
              <el-switch
                v-model="rule.enabled"
                size="small"
                @change="handleRuleToggle(rule)"
              />
            </div>
            <div class="rule-actions">
              <el-button size="small" text @click="editRule(rule, index)">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button size="small" text type="danger" @click="deleteRule(index)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
          <p class="rule-description">{{ rule.description }}</p>
          <div class="rule-meta">
            <el-tag
              v-for="status in rule.trigger.status"
              :key="status"
              size="small"
              type="warning"
            >
              {{ getStatusText(status) }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑规则对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingIndex >= 0 ? '编辑规则' : '添加规则'"
      width="600px"
    >
      <el-form :model="ruleForm" :rules="ruleRules" ref="ruleFormRef" label-width="120px">
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="ruleForm.name" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="规则描述" prop="description">
          <el-input
            v-model="ruleForm.description"
            type="textarea"
            :rows="2"
            placeholder="请输入规则描述"
          />
        </el-form-item>
        <el-form-item label="触发状态" prop="triggerStatus">
          <el-select
            v-model="ruleForm.triggerStatus"
            multiple
            placeholder="选择触发状态"
            style="width: 100%"
          >
            <el-option label="不通过" value="FAIL" />
            <el-option label="条件通过" value="CONDITIONAL_PASS" />
          </el-select>
        </el-form-item>
        <el-form-item label="标题模板" prop="titleTemplate">
          <el-input
            v-model="ruleForm.titleTemplate"
            placeholder="例如：${category} - ${content}"
          />
        </el-form-item>
        <el-form-item label="描述模板" prop="descriptionTemplate">
          <el-input
            v-model="ruleForm.descriptionTemplate"
            type="textarea"
            :rows="3"
            placeholder="例如：检查项：${content}&#10;不通过原因：${comment}"
          />
        </el-form-item>
        <el-form-item label="自动生成">
          <el-switch v-model="ruleForm.autoGenerate" />
          <span class="form-help">开启后将自动生成缺陷，否则需要手动确认</span>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="cancelEdit">取消</el-button>
        <el-button type="primary" @click="saveRule">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Plus, Edit, Delete, Setting } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { type DefectRule } from '@/types/defect-config'
import { ReviewItemStatus } from '@/api/review'

// Props
interface Props {
  modelValue: DefectRule[]
  templateId?: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [rules: DefectRule[]]
}>()

// Reactive data
const rules = ref<DefectRule[]>([])
const showAddDialog = ref(false)
const editingIndex = ref(-1)
const ruleFormRef = ref<FormInstance>()

// Form data
const ruleForm = ref({
  name: '',
  description: '',
  triggerStatus: [] as string[],
  titleTemplate: '',
  descriptionTemplate: '',
  autoGenerate: false
})

// Form rules
const ruleRules: FormRules = {
  name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入规则描述', trigger: 'blur' }],
  triggerStatus: [{ required: true, message: '请选择触发状态', trigger: 'change' }],
  titleTemplate: [{ required: true, message: '请输入标题模板', trigger: 'blur' }],
  descriptionTemplate: [{ required: true, message: '请输入描述模板', trigger: 'blur' }]
}

// Watch props
watch(
  () => props.modelValue,
  (newRules) => {
    rules.value = [...newRules]
  },
  { immediate: true }
)

// Watch rules and emit changes
watch(
  rules,
  (newRules) => {
    emit('update:modelValue', [...newRules])
  },
  { deep: true }
)

// Methods
const handleRuleToggle = (rule: DefectRule) => {
  // Rule is already updated by v-model
  ElMessage.success(`规则 "${rule.name}" 已${rule.enabled ? '启用' : '禁用'}`)
}

const editRule = (rule: DefectRule, index: number) => {
  editingIndex.value = index
  ruleForm.value = {
    name: rule.name,
    description: rule.description,
    triggerStatus: [...rule.trigger.status] as string[],
    titleTemplate: rule.template.titleTemplate,
    descriptionTemplate: rule.template.descriptionTemplate,
    autoGenerate: rule.options.autoGenerate
  }
  showAddDialog.value = true
}

const deleteRule = async (index: number) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除规则 "${rules.value[index].name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    rules.value.splice(index, 1)
    ElMessage.success('规则删除成功')
  } catch (error) {
    // User cancelled
  }
}

const saveRule = async () => {
  if (!ruleFormRef.value) return
  
  try {
    const valid = await ruleFormRef.value.validate()
    if (!valid) return
    
    const ruleData: DefectRule = {
      id: editingIndex.value >= 0 ? rules.value[editingIndex.value].id : `rule_${Date.now()}`,
      name: ruleForm.value.name,
      description: ruleForm.value.description,
      enabled: true,
      trigger: {
        status: ruleForm.value.triggerStatus as any[]
      },
      template: {
        titleTemplate: ruleForm.value.titleTemplate,
        descriptionTemplate: ruleForm.value.descriptionTemplate,
        severityMapping: {
          '安全': 'high',
          '功能': 'medium',
          '性能': 'medium',
          '界面': 'low',
          'default': 'medium'
        },
        categoryMapping: {
          '安全检查': 'security',
          '功能检查': 'functional',
          '性能检查': 'performance',
          '界面检查': 'ui',
          'default': 'general'
        },
        customFieldMapping: {}
      },
      options: {
        autoGenerate: ruleForm.value.autoGenerate,
        requireConfirmation: !ruleForm.value.autoGenerate,
        batchGenerate: true
      }
    }
    
    if (editingIndex.value >= 0) {
      rules.value[editingIndex.value] = ruleData
      ElMessage.success('规则更新成功')
    } else {
      rules.value.push(ruleData)
      ElMessage.success('规则添加成功')
    }
    
    showAddDialog.value = false
    resetForm()
  } catch (error) {
    // Validation failed
  }
}

const cancelEdit = () => {
  showAddDialog.value = false
  resetForm()
}

const resetForm = () => {
  editingIndex.value = -1
  ruleForm.value = {
    name: '',
    description: '',
    triggerStatus: [],
    titleTemplate: '',
    descriptionTemplate: '',
    autoGenerate: false
  }
}

const openAdvancedConfig = () => {
  const templateId = props.templateId
  const url = templateId
    ? `/admin/defect-rules?templateId=${templateId}`
    : '/admin/defect-rules'

  // 在新窗口打开，并监听窗口关闭事件来同步数据
  const newWindow = window.open(url, '_blank')

  if (newWindow) {
    // 监听窗口关闭，重新加载配置数据
    const checkClosed = setInterval(() => {
      if (newWindow.closed) {
        clearInterval(checkClosed)
        // 触发数据重新加载
        emit('reload-config')
        ElMessage.success('配置已同步')
      }
    }, 1000)
  }

  ElMessage.info('已在新窗口打开高级配置页面')
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    FAIL: '不通过',
    CONDITIONAL_PASS: '条件通过',
    PASS: '通过',
    PENDING: '待评审'
  }
  return statusMap[status] || status
}
</script>

<style scoped>
.defect-rule-config-embedded {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.header-info h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-weight: 600;
}

.header-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.rules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 12px;
}

.rule-card {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  transition: all 0.2s;
}

.rule-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.rule-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.rule-actions {
  display: flex;
  gap: 4px;
}

.rule-description {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
}

.rule-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.form-help {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

.feature-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 8px;
}

.basic-features,
.advanced-features {
  font-size: 14px;
}

.basic-features strong,
.advanced-features strong {
  color: #303133;
  display: block;
  margin-bottom: 8px;
}

.basic-features ul,
.advanced-features ul {
  margin: 0;
  padding-left: 16px;
  color: #606266;
}

.basic-features li,
.advanced-features li {
  margin-bottom: 4px;
  line-height: 1.4;
}
</style>
