<template>
  <div class="table-config-embedded">
    <div class="config-header">
      <div class="header-info">
        <h4>动态表头配置</h4>
        <p>配置评审表格的列显示和排序</p>
      </div>
      <el-button size="small" type="primary" @click="showConfigDialog = true">
        <el-icon><Setting /></el-icon>
        配置表头
      </el-button>
    </div>

    <!-- 当前配置预览 -->
    <div class="config-preview">
      <div v-if="config" class="config-summary">
        <div class="summary-item">
          <span class="label">配置名称：</span>
          <span class="value">{{ config.name }}</span>
        </div>
        <div class="summary-item">
          <span class="label">显示列数：</span>
          <span class="value">{{ visibleColumnsCount }}</span>
        </div>
        <div class="summary-item">
          <span class="label">分组字段：</span>
          <span class="value">{{ config.groupBy || '无' }}</span>
        </div>
      </div>
      
      <!-- 列配置预览 -->
      <div class="columns-preview">
        <h5>列配置预览</h5>
        <div class="columns-grid">
          <div
            v-for="column in config?.columns.filter(c => c.visible)"
            :key="column.id"
            class="column-item"
          >
            <span class="column-label">{{ column.label }}</span>
            <el-tag size="small" :type="getColumnTypeTag(column.type)">
              {{ getColumnTypeText(column.type) }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速配置 -->
    <div class="quick-config">
      <h5>快速配置</h5>
      <el-row :gutter="12">
        <el-col :span="12">
          <el-form-item label="分组字段">
            <el-select
              v-model="quickSettings.groupBy"
              placeholder="选择分组字段"
              clearable
              @change="updateQuickSetting"
            >
              <el-option label="分类" value="category" />
              <el-option label="状态" value="status" />
              <el-option label="评审人" value="reviewer" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="显示分组统计">
            <el-switch
              v-model="quickSettings.showGroupSummary"
              @change="updateQuickSetting"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </div>

    <!-- 详细配置对话框 -->
    <el-dialog
      v-model="showConfigDialog"
      title="详细表头配置"
      width="700px"
    >
      <div class="detailed-config">
        <el-alert
          title="提示"
          type="info"
          :closable="false"
          style="margin-bottom: 16px"
        >
          在这里可以进行更详细的列配置，包括添加自定义列、设置列宽等。
        </el-alert>
        
        <div class="columns-config">
          <h5>可见列配置</h5>
          <div class="columns-list">
            <div
              v-for="column in config?.columns"
              :key="column.id"
              class="column-config-item"
            >
              <div class="column-info">
                <el-checkbox
                  v-model="column.visible"
                  :disabled="column.fixed"
                  @change="updateConfig"
                >
                  {{ column.label }}
                </el-checkbox>
                <el-tag size="small" :type="getColumnTypeTag(column.type)">
                  {{ getColumnTypeText(column.type) }}
                </el-tag>
              </div>
              <div class="column-controls">
                <el-input-number
                  v-if="column.width"
                  v-model="column.width"
                  :min="60"
                  :max="500"
                  size="small"
                  style="width: 80px"
                  @change="updateConfig"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showConfigDialog = false">关闭</el-button>
        <el-button type="primary" @click="resetToDefault">重置为默认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Setting } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { type TableViewConfig, DEFAULT_TABLE_CONFIG } from '@/types/table-config'

// Props
interface Props {
  modelValue: TableViewConfig | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [config: TableViewConfig | null]
}>()

// Reactive data
const config = ref<TableViewConfig | null>(null)
const showConfigDialog = ref(false)

// Quick settings
const quickSettings = ref({
  groupBy: '',
  showGroupSummary: true
})

// Computed
const visibleColumnsCount = computed(() => {
  return config.value?.columns.filter(c => c.visible).length || 0
})

// Watch props
watch(
  () => props.modelValue,
  (newConfig) => {
    config.value = newConfig ? { ...newConfig } : { ...DEFAULT_TABLE_CONFIG }
    quickSettings.value = {
      groupBy: newConfig?.groupBy || '',
      showGroupSummary: newConfig?.showGroupSummary ?? true
    }
  },
  { immediate: true }
)

// Methods
const updateConfig = () => {
  if (config.value) {
    emit('update:modelValue', { ...config.value })
  }
}

const updateQuickSetting = () => {
  if (config.value) {
    config.value.groupBy = quickSettings.value.groupBy || undefined
    config.value.showGroupSummary = quickSettings.value.showGroupSummary
    updateConfig()
  }
}

const resetToDefault = () => {
  config.value = { ...DEFAULT_TABLE_CONFIG }
  quickSettings.value = {
    groupBy: DEFAULT_TABLE_CONFIG.groupBy || '',
    showGroupSummary: DEFAULT_TABLE_CONFIG.showGroupSummary ?? true
  }
  updateConfig()
  showConfigDialog.value = false
  ElMessage.success('已重置为默认配置')
}

const getColumnTypeTag = (type: string) => {
  const typeMap: Record<string, string> = {
    text: 'info',
    number: 'success',
    date: 'warning',
    tag: 'primary',
    status: 'danger'
  }
  return typeMap[type] || 'info'
}

const getColumnTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    text: '文本',
    number: '数字',
    date: '日期',
    tag: '标签',
    status: '状态'
  }
  return typeMap[type] || type
}
</script>

<style scoped>
.table-config-embedded {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.header-info h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-weight: 600;
}

.header-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.config-preview {
  margin-bottom: 16px;
}

.config-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 12px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.summary-item .label {
  color: #606266;
  font-size: 14px;
}

.summary-item .value {
  color: #303133;
  font-weight: 600;
}

.columns-preview h5 {
  margin: 0 0 8px 0;
  color: #303133;
  font-weight: 600;
}

.columns-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.column-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  font-size: 12px;
}

.column-label {
  color: #303133;
}

.quick-config h5 {
  margin: 0 0 12px 0;
  color: #303133;
  font-weight: 600;
}

.detailed-config h5 {
  margin: 0 0 12px 0;
  color: #303133;
  font-weight: 600;
}

.columns-list {
  max-height: 300px;
  overflow-y: auto;
}

.column-config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
  background: #fff;
}

.column-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.column-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
