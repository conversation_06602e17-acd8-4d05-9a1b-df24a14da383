<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="90%"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <!-- 选项卡导航 -->
    <el-tabs v-model="activeTab" type="border-card" class="template-tabs">
      <!-- 基本信息选项卡 -->
      <el-tab-pane label="基本信息" name="basic">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
          @submit.prevent
        >
          <!-- Basic Information -->
          <el-card class="form-section" shadow="never">
            <template #header>
              <span class="section-title">基本信息</span>
            </template>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="模板名称" prop="name">
                  <el-input
                    v-model="formData.name"
                    placeholder="请输入模板名称"
                    maxlength="100"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="模板类型" prop="type">
                  <el-select
                    v-model="formData.type"
                    placeholder="请选择或输入模板类型"
                    filterable
                    allow-create
                    style="width: 100%"
                  >
                    <el-option
                      v-for="type in templateTypes"
                      :key="type"
                      :label="type"
                      :value="type"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>

          <!-- Checklist Items -->
          <el-card class="form-section" shadow="never">
        <template #header>
          <div class="section-header">
            <span class="section-title">检查项配置</span>
            <div class="section-actions">
              <el-button size="small" @click="handleAddItem">
                <el-icon><Plus /></el-icon>
                添加检查项
              </el-button>
              <el-button size="small" @click="handleBatchAdd">
                <el-icon><DocumentAdd /></el-icon>
                批量添加
              </el-button>
            </div>
          </div>
        </template>

        <!-- Items list -->
        <div v-if="formData.items.length > 0" class="items-container">
          <draggable
            v-model="formData.items"
            item-key="tempId"
            handle=".drag-handle"
            @end="handleDragEnd"
          >
            <template #item="{ element: item, index }">
              <div class="item-card">
                <div class="item-header">
                  <div class="item-info">
                    <el-icon class="drag-handle"><Rank /></el-icon>
                    <span class="item-sequence">{{ index + 1 }}</span>
                    <el-tag v-if="item.category" size="small" type="info">
                      {{ item.category }}
                    </el-tag>
                    <el-tag v-if="item.required" size="small" type="danger">
                      必填
                    </el-tag>
                  </div>
                  <div class="item-actions">
                    <el-button size="small" text @click="handleEditItem(index)">
                      <el-icon><Edit /></el-icon>
                    </el-button>
                    <el-button size="small" text type="danger" @click="handleDeleteItem(index)">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                </div>
                <div class="item-content">
                  {{ item.content }}
                </div>
              </div>
            </template>
          </draggable>
        </div>

        <!-- Empty state -->
        <el-empty
          v-else
          description="暂无检查项"
          :image-size="80"
        >
          <el-button type="primary" @click="handleAddItem">
            添加第一个检查项
          </el-button>
        </el-empty>
      </el-card>
        </el-form>
      </el-tab-pane>

      <!-- 缺陷生成规则选项卡 -->
      <el-tab-pane label="缺陷生成规则" name="defect-rules">
        <DefectRuleConfig
          v-model:rules="formData.defectRules"
          :template-id="props.template?.id"
          embedded
        />
      </el-tab-pane>

      <!-- 动态表头配置选项卡 -->
      <el-tab-pane label="动态表头配置" name="table-config">
        <TableColumnConfig
          v-model:config="formData.tableConfig"
          :template-id="props.template?.id"
          embedded
        />
      </el-tab-pane>

      <!-- 自定义按钮配置选项卡 -->
      <el-tab-pane label="自定义按钮" name="custom-buttons">
        <StatusButtonConfig
          v-model:buttons="formData.customButtons"
          :template-id="props.template?.id"
          embedded
        />
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="saving" @click="handleSave">
          {{ mode === 'create' ? '创建' : '保存' }}
        </el-button>
      </div>
    </template>

    <!-- Item form dialog -->
    <el-dialog
      v-model="itemFormVisible"
      :title="itemFormMode === 'create' ? '添加检查项' : '编辑检查项'"
      width="600px"
      :before-close="handleItemFormClose"
    >
      <el-form
        ref="itemFormRef"
        :model="itemFormData"
        :rules="itemFormRules"
        label-width="80px"
      >
        <el-form-item label="检查内容" prop="content">
          <el-input
            v-model="itemFormData.content"
            type="textarea"
            :rows="3"
            placeholder="请输入检查项内容"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select
            v-model="itemFormData.category"
            placeholder="请选择或输入分类"
            filterable
            allow-create
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="category in itemCategories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否必填">
          <el-switch v-model="itemFormData.required" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="handleItemFormClose">取消</el-button>
        <el-button type="primary" @click="handleItemSave">
          {{ itemFormMode === 'create' ? '添加' : '保存' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- Batch add dialog -->
    <el-dialog
      v-model="batchAddVisible"
      title="批量添加检查项"
      width="600px"
      :before-close="handleBatchAddClose"
    >
      <div class="batch-add-content">
        <el-alert
          title="批量添加说明"
          type="info"
          :closable="false"
          style="margin-bottom: 16px"
        >
          <p>每行一个检查项，支持以下格式：</p>
          <p>• 简单格式：检查项内容</p>
          <p>• 带分类：[分类名称] 检查项内容</p>
          <p>• 必填项：检查项内容 *</p>
          <p>• 完整格式：[分类名称] 检查项内容 *</p>
        </el-alert>
        
        <el-input
          v-model="batchAddText"
          type="textarea"
          :rows="10"
          placeholder="请输入检查项内容，每行一个..."
        />
        
        <div class="batch-preview" v-if="batchPreviewItems.length > 0">
          <h4>预览 ({{ batchPreviewItems.length }} 项)</h4>
          <div class="preview-list">
            <div
              v-for="(item, index) in batchPreviewItems"
              :key="index"
              class="preview-item"
            >
              <span class="preview-sequence">{{ index + 1 }}.</span>
              <span class="preview-content">{{ item.content }}</span>
              <el-tag v-if="item.category" size="small" type="info">
                {{ item.category }}
              </el-tag>
              <el-tag v-if="item.required" size="small" type="danger">
                必填
              </el-tag>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="handleBatchAddClose">取消</el-button>
        <el-button
          type="primary"
          :disabled="batchPreviewItems.length === 0"
          @click="handleBatchAddConfirm"
        >
          添加 {{ batchPreviewItems.length }} 项
        </el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  Plus,
  Edit,
  Delete,
  DocumentAdd,
  Rank,
} from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import {
  createTemplate,
  updateTemplate,
  getTemplateTypes,
  type ChecklistTemplate,
  type ChecklistItem,
  type CreateTemplateRequest,
  type UpdateTemplateRequest,
} from '@/api/template'
import DefectRuleConfig from '@/views/admin/DefectRuleConfig.vue'
import TableColumnConfig from '@/components/review/TableColumnConfig.vue'
import StatusButtonConfig from '@/views/admin/StatusButtonConfig.vue'
import type { DefectRule } from '@/types/defect-config'
import type { TableViewConfig } from '@/types/table-config'
import type { StatusButtonGroup } from '@/types/defect-config'

// Props
interface Props {
  visible: boolean
  template?: ChecklistTemplate | null
  mode: 'create' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  template: null,
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// Refs
const formRef = ref<FormInstance>()
const itemFormRef = ref<FormInstance>()

// Reactive data
const dialogVisible = ref(false)
const saving = ref(false)
const templateTypes = ref<string[]>([])
const activeTab = ref('basic')

// Form data
interface FormData {
  name: string
  type: string
  items: (ChecklistItem & { tempId: string })[]
  defectRules: DefectRule[]
  tableConfig: TableViewConfig | null
  customButtons: StatusButtonGroup[]
}

const formData = ref<FormData>({
  name: '',
  type: '',
  items: [],
  defectRules: [],
  tableConfig: null,
  customButtons: [],
})

// Form rules
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 1, max: 100, message: '模板名称长度在 1 到 100 个字符', trigger: 'blur' },
  ],
  type: [
    { required: true, message: '请选择模板类型', trigger: 'change' },
  ],
}

// Item form
const itemFormVisible = ref(false)
const itemFormMode = ref<'create' | 'edit'>('create')
const currentItemIndex = ref(-1)
const itemCategories = ref<string[]>([])

interface ItemFormData {
  content: string
  category: string
  required: boolean
}

const itemFormData = ref<ItemFormData>({
  content: '',
  category: '',
  required: false,
})

const itemFormRules: FormRules = {
  content: [
    { required: true, message: '请输入检查项内容', trigger: 'blur' },
    { min: 1, max: 500, message: '检查项内容长度在 1 到 500 个字符', trigger: 'blur' },
  ],
}

// Batch add
const batchAddVisible = ref(false)
const batchAddText = ref('')

// Computed
const dialogTitle = computed(() => {
  return props.mode === 'create' ? '创建模板' : '编辑模板'
})

const batchPreviewItems = computed(() => {
  if (!batchAddText.value.trim()) return []
  
  const lines = batchAddText.value.split('\n').filter(line => line.trim())
  return lines.map(line => {
    const trimmed = line.trim()
    let content = trimmed
    let category = ''
    let required = false
    
    // Check for required marker (*)
    if (trimmed.endsWith(' *')) {
      required = true
      content = trimmed.slice(0, -2).trim()
    }
    
    // Check for category [category]
    const categoryMatch = content.match(/^\[([^\]]+)\]\s*(.+)$/)
    if (categoryMatch) {
      category = categoryMatch[1].trim()
      content = categoryMatch[2].trim()
    }
    
    return {
      content,
      category,
      required,
    }
  }).filter(item => item.content)
})

// Watch props
watch(
  () => props.visible,
  (visible) => {
    dialogVisible.value = visible
    if (visible) {
      initForm()
      loadTemplateTypes()
    }
  },
  { immediate: true }
)

watch(dialogVisible, (visible) => {
  emit('update:visible', visible)
})

// Methods
const initForm = () => {
  if (props.mode === 'edit' && props.template) {
    formData.value = {
      name: props.template.name,
      type: props.template.type,
      items: props.template.items.map((item, index) => ({
        ...item,
        tempId: `item_${Date.now()}_${index}`,
      })),
      defectRules: (props.template as any).defectRules || [],
      tableConfig: (props.template as any).tableConfig || null,
      customButtons: (props.template as any).customButtons || [],
    }
  } else {
    formData.value = {
      name: '',
      type: '',
      items: [],
      defectRules: [],
      tableConfig: null,
      customButtons: [],
    }
  }

  // Extract categories from existing items
  const categories = new Set<string>()
  formData.value.items.forEach(item => {
    if (item.category) {
      categories.add(item.category)
    }
  })
  itemCategories.value = Array.from(categories)

  // 重置到基本信息选项卡
  activeTab.value = 'basic'
}

const loadTemplateTypes = async () => {
  try {
    templateTypes.value = await getTemplateTypes()
  } catch (error) {
    console.error('Failed to load template types:', error)
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    if (formData.value.items.length === 0) {
      ElMessage.warning('请至少添加一个检查项')
      return
    }
    
    saving.value = true
    
    // Prepare items data
    const items = formData.value.items.map((item, index) => ({
      id: item.id || `item_${Date.now()}_${index}`,
      sequence: index + 1,
      content: item.content,
      required: item.required,
      category: item.category || '',
    }))
    
    if (props.mode === 'create') {
      const request: CreateTemplateRequest & {
        defectRules?: DefectRule[]
        tableConfig?: TableViewConfig | null
        customButtons?: StatusButtonGroup[]
      } = {
        name: formData.value.name,
        type: formData.value.type,
        items,
        defectRules: formData.value.defectRules,
        tableConfig: formData.value.tableConfig,
        customButtons: formData.value.customButtons,
      }
      await createTemplate(request)
      ElMessage.success('模板创建成功')
    } else if (props.template) {
      const request: UpdateTemplateRequest & {
        defectRules?: DefectRule[]
        tableConfig?: TableViewConfig | null
        customButtons?: StatusButtonGroup[]
      } = {
        name: formData.value.name,
        type: formData.value.type,
        items,
        defectRules: formData.value.defectRules,
        tableConfig: formData.value.tableConfig,
        customButtons: formData.value.customButtons,
      }
      await updateTemplate(props.template.id, request)
      ElMessage.success('模板更新成功')
    }
    
    emit('success')
  } catch (error) {
    console.error('Failed to save template:', error)
  } finally {
    saving.value = false
  }
}

const handleAddItem = () => {
  itemFormData.value = {
    content: '',
    category: '',
    required: false,
  }
  itemFormMode.value = 'create'
  currentItemIndex.value = -1
  itemFormVisible.value = true
}

const handleEditItem = (index: number) => {
  const item = formData.value.items[index]
  itemFormData.value = {
    content: item.content,
    category: item.category || '',
    required: item.required,
  }
  itemFormMode.value = 'edit'
  currentItemIndex.value = index
  itemFormVisible.value = true
}

const handleDeleteItem = async (index: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个检查项吗？',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    formData.value.items.splice(index, 1)
    ElMessage.success('检查项删除成功')
  } catch (error) {
    // User cancelled
  }
}

const handleItemFormClose = () => {
  itemFormVisible.value = false
}

const handleItemSave = async () => {
  if (!itemFormRef.value) return
  
  try {
    const valid = await itemFormRef.value.validate()
    if (!valid) return
    
    const newItem = {
      id: itemFormMode.value === 'create' ? `item_${Date.now()}_${Math.random()}` : formData.value.items[currentItemIndex.value].id,
      tempId: `item_${Date.now()}_${Math.random()}`,
      sequence: 0, // Will be updated when saving
      content: itemFormData.value.content,
      category: itemFormData.value.category,
      required: itemFormData.value.required,
    }
    
    if (itemFormMode.value === 'create') {
      formData.value.items.push(newItem)
      ElMessage.success('检查项添加成功')
    } else {
      formData.value.items[currentItemIndex.value] = newItem
      ElMessage.success('检查项更新成功')
    }
    
    // Update categories
    if (newItem.category && !itemCategories.value.includes(newItem.category)) {
      itemCategories.value.push(newItem.category)
    }
    
    itemFormVisible.value = false
  } catch (error) {
    console.error('Failed to save item:', error)
  }
}

const handleBatchAdd = () => {
  batchAddText.value = ''
  batchAddVisible.value = true
}

const handleBatchAddClose = () => {
  batchAddVisible.value = false
}

const handleBatchAddConfirm = () => {
  const newItems = batchPreviewItems.value.map((item, index) => ({
    id: `item_${Date.now()}_${index}`,
    tempId: `item_${Date.now()}_${index}`,
    sequence: 0,
    content: item.content,
    category: item.category,
    required: item.required,
  }))
  
  formData.value.items.push(...newItems)
  
  // Update categories
  newItems.forEach(item => {
    if (item.category && !itemCategories.value.includes(item.category)) {
      itemCategories.value.push(item.category)
    }
  })
  
  ElMessage.success(`成功添加 ${newItems.length} 个检查项`)
  batchAddVisible.value = false
}

const handleDragEnd = () => {
  // Items are automatically reordered by draggable
  ElMessage.success('检查项顺序已更新')
}
</script>

<style scoped>
.template-tabs {
  margin-bottom: 20px;
}

.template-tabs :deep(.el-tabs__content) {
  padding: 20px 0;
}

.template-tabs :deep(.el-tab-pane) {
  max-height: 60vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 20px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-weight: 600;
  color: #303133;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.section-actions {
  display: flex;
  gap: 8px;
}

.items-container {
  max-height: 400px;
  overflow-y: auto;
}

.item-card {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  background: #fafafa;
  transition: all 0.2s;
}

.item-card:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.item-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.drag-handle {
  cursor: move;
  color: #909399;
}

.item-sequence {
  font-weight: 600;
  color: #606266;
  min-width: 20px;
}

.item-actions {
  display: flex;
  gap: 4px;
}

.item-content {
  color: #303133;
  line-height: 1.5;
  padding-left: 28px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.batch-add-content {
  max-height: 60vh;
  overflow-y: auto;
}

.batch-preview {
  margin-top: 16px;
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

.batch-preview h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.preview-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
  background: #fafafa;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  font-size: 14px;
}

.preview-sequence {
  color: #909399;
  min-width: 20px;
}

.preview-content {
  flex: 1;
  color: #303133;
}

:deep(.el-card__header) {
  padding: 12px 16px;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}
</style>