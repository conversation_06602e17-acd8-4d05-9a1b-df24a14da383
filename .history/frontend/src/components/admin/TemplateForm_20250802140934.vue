<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="90%"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <!-- 选项卡导航 -->
    <el-tabs v-model="activeTab" type="border-card" class="template-tabs">
      <!-- 基本信息选项卡 -->
      <el-tab-pane label="基本信息" name="basic">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
          @submit.prevent
        >
          <!-- Basic Information -->
          <el-card class="form-section" shadow="never">
            <template #header>
              <span class="section-title">基本信息</span>
            </template>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="模板名称" prop="name">
                  <el-input
                    v-model="formData.name"
                    placeholder="请输入模板名称"
                    maxlength="100"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="模板类型" prop="type">
                  <el-select
                    v-model="formData.type"
                    placeholder="请选择或输入模板类型"
                    filterable
                    allow-create
                    style="width: 100%"
                  >
                    <el-option
                      v-for="type in templateTypes"
                      :key="type"
                      :label="type"
                      :value="type"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>

          <!-- Checklist Items -->
          <el-card class="form-section" shadow="never">
        <template #header>
          <div class="section-header">
            <span class="section-title">检查项配置</span>
            <div class="section-actions">
              <el-button size="small" @click="handleAddItem">
                <el-icon><Plus /></el-icon>
                添加检查项
              </el-button>
              <el-button size="small" @click="handleBatchAdd">
                <el-icon><DocumentAdd /></el-icon>
                批量添加
              </el-button>
            </div>
          </div>
        </template>

        <!-- Items list -->
        <div v-if="formData.items.length > 0" class="items-container">
          <draggable
            v-model="formData.items"
            item-key="tempId"
            handle=".drag-handle"
            @end="handleDragEnd"
          >
            <template #item="{ element: item, index }">
              <div class="item-card">
                <div class="item-header">
                  <div class="item-info">
                    <el-icon class="drag-handle"><Rank /></el-icon>
                    <span class="item-sequence">{{ index + 1 }}</span>
                    <el-tag v-if="item.category" size="small" type="info">
                      {{ item.category }}
                    </el-tag>
                    <el-tag v-if="item.required" size="small" type="danger">
                      必填
                    </el-tag>
                  </div>
                  <div class="item-actions">
                    <el-button size="small" text @click="handleEditItem(index)">
                      <el-icon><Edit /></el-icon>
                    </el-button>
                    <el-button size="small" text type="danger" @click="handleDeleteItem(index)">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                </div>
                <div class="item-content">
                  {{ item.content }}
                </div>
              </div>
            </template>
          </draggable>
        </div>

        <!-- Empty state -->
        <el-empty
          v-else
          description="暂无检查项"
          :image-size="80"
        >
          <el-button type="primary" @click="handleAddItem">
            添加第一个检查项
          </el-button>
        </el-empty>
      </el-card>
        </el-form>
      </el-tab-pane>

      <!-- 高级配置选项卡 -->
      <el-tab-pane label="高级配置" name="advanced-config">
        <div class="advanced-config-content">
          <el-alert
            title="高级配置说明"
            type="info"
            :closable="false"
            style="margin-bottom: 20px"
          >
            <p>高级配置包括缺陷生成规则、动态表头设置和自定义按钮配置。</p>
            <p>这些配置将与当前模版关联，在评审过程中生效。</p>
          </el-alert>

          <!-- 配置概览 -->
          <div class="config-overview">
            <el-row :gutter="16">
              <el-col :span="8">
                <el-card class="config-card" shadow="hover">
                  <div class="config-item">
                    <div class="config-icon">🐛</div>
                    <div class="config-info">
                      <h4>缺陷生成规则</h4>
                      <p>{{ formData.defectRules.length }} 个规则</p>
                    </div>
                  </div>
                  <div class="config-actions">
                    <el-button size="small" @click="openDefectRuleConfig">
                      配置规则
                    </el-button>
                  </div>
                </el-card>
              </el-col>

              <el-col :span="8">
                <el-card class="config-card" shadow="hover">
                  <div class="config-item">
                    <div class="config-icon">📊</div>
                    <div class="config-info">
                      <h4>动态表头配置</h4>
                      <p>{{ formData.tableConfig ? '已配置' : '使用默认' }}</p>
                    </div>
                  </div>
                  <div class="config-actions">
                    <el-button size="small" @click="openTableConfig">
                      配置表头
                    </el-button>
                  </div>
                </el-card>
              </el-col>

              <el-col :span="8">
                <el-card class="config-card" shadow="hover">
                  <div class="config-item">
                    <div class="config-icon">🔘</div>
                    <div class="config-info">
                      <h4>自定义按钮</h4>
                      <p>{{ formData.customButtons.length }} 个按钮组</p>
                    </div>
                  </div>
                  <div class="config-actions">
                    <el-button size="small" @click="openButtonConfig">
                      配置按钮
                    </el-button>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>

          <!-- 配置详情预览 -->
          <div class="config-details">
            <el-collapse v-model="activeConfigPanels">
              <el-collapse-item title="缺陷生成规则详情" name="defect-rules">
                <div v-if="formData.defectRules.length > 0" class="rules-preview">
                  <div
                    v-for="rule in formData.defectRules"
                    :key="rule.id"
                    class="rule-preview-item"
                  >
                    <div class="rule-header">
                      <span class="rule-name">{{ rule.name }}</span>
                      <el-tag :type="rule.enabled ? 'success' : 'info'" size="small">
                        {{ rule.enabled ? '启用' : '禁用' }}
                      </el-tag>
                    </div>
                    <p class="rule-description">{{ rule.description }}</p>
                  </div>
                </div>
                <el-empty v-else description="暂无缺陷生成规则" :image-size="60" />
              </el-collapse-item>

              <el-collapse-item title="动态表头配置详情" name="table-config">
                <div v-if="formData.tableConfig" class="table-config-preview">
                  <p><strong>配置名称：</strong>{{ formData.tableConfig.name }}</p>
                  <p><strong>列数量：</strong>{{ formData.tableConfig.columns.length }}</p>
                  <p><strong>分组字段：</strong>{{ formData.tableConfig.groupBy || '无' }}</p>
                </div>
                <el-empty v-else description="使用默认表头配置" :image-size="60" />
              </el-collapse-item>

              <el-collapse-item title="自定义按钮详情" name="custom-buttons">
                <div v-if="formData.customButtons.length > 0" class="buttons-preview">
                  <div
                    v-for="group in formData.customButtons"
                    :key="group.id"
                    class="button-group-preview"
                  >
                    <h5>{{ group.name }}</h5>
                    <div class="buttons-list">
                      <el-button
                        v-for="button in group.buttons"
                        :key="button.id"
                        :type="button.type"
                        size="small"
                        disabled
                      >
                        {{ button.label }}
                      </el-button>
                    </div>
                  </div>
                </div>
                <el-empty v-else description="暂无自定义按钮" :image-size="60" />
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="saving" @click="handleSave">
          {{ mode === 'create' ? '创建' : '保存' }}
        </el-button>
      </div>
    </template>

    <!-- Item form dialog -->
    <el-dialog
      v-model="itemFormVisible"
      :title="itemFormMode === 'create' ? '添加检查项' : '编辑检查项'"
      width="600px"
      :before-close="handleItemFormClose"
    >
      <el-form
        ref="itemFormRef"
        :model="itemFormData"
        :rules="itemFormRules"
        label-width="80px"
      >
        <el-form-item label="检查内容" prop="content">
          <el-input
            v-model="itemFormData.content"
            type="textarea"
            :rows="3"
            placeholder="请输入检查项内容"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select
            v-model="itemFormData.category"
            placeholder="请选择或输入分类"
            filterable
            allow-create
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="category in itemCategories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否必填">
          <el-switch v-model="itemFormData.required" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="handleItemFormClose">取消</el-button>
        <el-button type="primary" @click="handleItemSave">
          {{ itemFormMode === 'create' ? '添加' : '保存' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- Batch add dialog -->
    <el-dialog
      v-model="batchAddVisible"
      title="批量添加检查项"
      width="600px"
      :before-close="handleBatchAddClose"
    >
      <div class="batch-add-content">
        <el-alert
          title="批量添加说明"
          type="info"
          :closable="false"
          style="margin-bottom: 16px"
        >
          <p>每行一个检查项，支持以下格式：</p>
          <p>• 简单格式：检查项内容</p>
          <p>• 带分类：[分类名称] 检查项内容</p>
          <p>• 必填项：检查项内容 *</p>
          <p>• 完整格式：[分类名称] 检查项内容 *</p>
        </el-alert>
        
        <el-input
          v-model="batchAddText"
          type="textarea"
          :rows="10"
          placeholder="请输入检查项内容，每行一个..."
        />
        
        <div class="batch-preview" v-if="batchPreviewItems.length > 0">
          <h4>预览 ({{ batchPreviewItems.length }} 项)</h4>
          <div class="preview-list">
            <div
              v-for="(item, index) in batchPreviewItems"
              :key="index"
              class="preview-item"
            >
              <span class="preview-sequence">{{ index + 1 }}.</span>
              <span class="preview-content">{{ item.content }}</span>
              <el-tag v-if="item.category" size="small" type="info">
                {{ item.category }}
              </el-tag>
              <el-tag v-if="item.required" size="small" type="danger">
                必填
              </el-tag>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="handleBatchAddClose">取消</el-button>
        <el-button
          type="primary"
          :disabled="batchPreviewItems.length === 0"
          @click="handleBatchAddConfirm"
        >
          添加 {{ batchPreviewItems.length }} 项
        </el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  Plus,
  Edit,
  Delete,
  DocumentAdd,
  Rank,
} from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import {
  createTemplate,
  updateTemplate,
  getTemplateTypes,
  type ChecklistTemplate,
  type ChecklistItem,
  type CreateTemplateRequest,
  type UpdateTemplateRequest,
} from '@/api/template'
import type { DefectRule } from '@/types/defect-config'
import type { TableViewConfig } from '@/types/table-config'
import type { StatusButtonGroup } from '@/types/defect-config'
import {
  getTemplateDefectRules,
  getTemplateTableConfig,
  getTemplateButtonGroups
} from '@/api/admin-config'

// Props
interface Props {
  visible: boolean
  template?: ChecklistTemplate | null
  mode: 'create' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  template: null,
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// Refs
const formRef = ref<FormInstance>()
const itemFormRef = ref<FormInstance>()

// Reactive data
const dialogVisible = ref(false)
const saving = ref(false)
const templateTypes = ref<string[]>([])
const activeTab = ref('basic')
const activeConfigPanels = ref<string[]>(['defect-rules'])

// Form data
interface FormData {
  name: string
  type: string
  items: (ChecklistItem & { tempId: string })[]
  defectRules: DefectRule[]
  tableConfig: TableViewConfig | null
  customButtons: StatusButtonGroup[]
}

const formData = ref<FormData>({
  name: '',
  type: '',
  items: [],
  defectRules: [],
  tableConfig: null,
  customButtons: [],
})

// Form rules
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 1, max: 100, message: '模板名称长度在 1 到 100 个字符', trigger: 'blur' },
  ],
  type: [
    { required: true, message: '请选择模板类型', trigger: 'change' },
  ],
}

// Item form
const itemFormVisible = ref(false)
const itemFormMode = ref<'create' | 'edit'>('create')
const currentItemIndex = ref(-1)
const itemCategories = ref<string[]>([])

interface ItemFormData {
  content: string
  category: string
  required: boolean
}

const itemFormData = ref<ItemFormData>({
  content: '',
  category: '',
  required: false,
})

const itemFormRules: FormRules = {
  content: [
    { required: true, message: '请输入检查项内容', trigger: 'blur' },
    { min: 1, max: 500, message: '检查项内容长度在 1 到 500 个字符', trigger: 'blur' },
  ],
}

// Batch add
const batchAddVisible = ref(false)
const batchAddText = ref('')

// Computed
const dialogTitle = computed(() => {
  return props.mode === 'create' ? '创建模板' : '编辑模板'
})

const batchPreviewItems = computed(() => {
  if (!batchAddText.value.trim()) return []
  
  const lines = batchAddText.value.split('\n').filter(line => line.trim())
  return lines.map(line => {
    const trimmed = line.trim()
    let content = trimmed
    let category = ''
    let required = false
    
    // Check for required marker (*)
    if (trimmed.endsWith(' *')) {
      required = true
      content = trimmed.slice(0, -2).trim()
    }
    
    // Check for category [category]
    const categoryMatch = content.match(/^\[([^\]]+)\]\s*(.+)$/)
    if (categoryMatch) {
      category = categoryMatch[1].trim()
      content = categoryMatch[2].trim()
    }
    
    return {
      content,
      category,
      required,
    }
  }).filter(item => item.content)
})

// Watch props
watch(
  () => props.visible,
  (visible) => {
    dialogVisible.value = visible
    if (visible) {
      initForm()
      loadTemplateTypes()
    }
  },
  { immediate: true }
)

watch(dialogVisible, (visible) => {
  emit('update:visible', visible)
})

// Methods
const initForm = async () => {
  if (props.mode === 'edit' && props.template) {
    formData.value = {
      name: props.template.name,
      type: props.template.type,
      items: props.template.items.map((item, index) => ({
        ...item,
        tempId: `item_${Date.now()}_${index}`,
      })),
      defectRules: [],
      tableConfig: null,
      customButtons: [],
    }

    // 加载高级配置数据
    await loadAdvancedConfig(props.template.id)
  } else {
    formData.value = {
      name: '',
      type: '',
      items: [],
      defectRules: [],
      tableConfig: null,
      customButtons: [],
    }
  }

  // Extract categories from existing items
  const categories = new Set<string>()
  formData.value.items.forEach(item => {
    if (item.category) {
      categories.add(item.category)
    }
  })
  itemCategories.value = Array.from(categories)

  // 重置到基本信息选项卡
  activeTab.value = 'basic'
}

// 加载高级配置数据
const loadAdvancedConfig = async (templateId: string) => {
  try {
    // 并行加载所有配置
    const [defectRules, tableConfig, buttonGroups] = await Promise.allSettled([
      getTemplateDefectRules(templateId),
      getTemplateTableConfig(templateId),
      getTemplateButtonGroups(templateId)
    ])

    if (defectRules.status === 'fulfilled') {
      formData.value.defectRules = defectRules.value || []
    }

    if (tableConfig.status === 'fulfilled') {
      formData.value.tableConfig = tableConfig.value
    }

    if (buttonGroups.status === 'fulfilled') {
      formData.value.customButtons = buttonGroups.value || []
    }
  } catch (error) {
    console.error('Failed to load advanced config:', error)
    // 不显示错误消息，因为这些配置是可选的
  }
}

const loadTemplateTypes = async () => {
  try {
    templateTypes.value = await getTemplateTypes()
  } catch (error) {
    console.error('Failed to load template types:', error)
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    if (formData.value.items.length === 0) {
      ElMessage.warning('请至少添加一个检查项')
      return
    }
    
    saving.value = true
    
    // Prepare items data
    const items = formData.value.items.map((item, index) => ({
      id: item.id || `item_${Date.now()}_${index}`,
      sequence: index + 1,
      content: item.content,
      required: item.required,
      category: item.category || '',
    }))
    
    if (props.mode === 'create') {
      const request: CreateTemplateRequest & {
        defectRules?: DefectRule[]
        tableConfig?: TableViewConfig | null
        customButtons?: StatusButtonGroup[]
      } = {
        name: formData.value.name,
        type: formData.value.type,
        items,
        defectRules: formData.value.defectRules,
        tableConfig: formData.value.tableConfig,
        customButtons: formData.value.customButtons,
      }
      await createTemplate(request)
      ElMessage.success('模板创建成功')
    } else if (props.template) {
      const request: UpdateTemplateRequest & {
        defectRules?: DefectRule[]
        tableConfig?: TableViewConfig | null
        customButtons?: StatusButtonGroup[]
      } = {
        name: formData.value.name,
        type: formData.value.type,
        items,
        defectRules: formData.value.defectRules,
        tableConfig: formData.value.tableConfig,
        customButtons: formData.value.customButtons,
      }
      await updateTemplate(props.template.id, request)
      ElMessage.success('模板更新成功')
    }
    
    emit('success')
  } catch (error) {
    console.error('Failed to save template:', error)
  } finally {
    saving.value = false
  }
}

const handleAddItem = () => {
  itemFormData.value = {
    content: '',
    category: '',
    required: false,
  }
  itemFormMode.value = 'create'
  currentItemIndex.value = -1
  itemFormVisible.value = true
}

const handleEditItem = (index: number) => {
  const item = formData.value.items[index]
  itemFormData.value = {
    content: item.content,
    category: item.category || '',
    required: item.required,
  }
  itemFormMode.value = 'edit'
  currentItemIndex.value = index
  itemFormVisible.value = true
}

const handleDeleteItem = async (index: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个检查项吗？',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    formData.value.items.splice(index, 1)
    ElMessage.success('检查项删除成功')
  } catch (error) {
    // User cancelled
  }
}

const handleItemFormClose = () => {
  itemFormVisible.value = false
}

const handleItemSave = async () => {
  if (!itemFormRef.value) return
  
  try {
    const valid = await itemFormRef.value.validate()
    if (!valid) return
    
    const newItem = {
      id: itemFormMode.value === 'create' ? `item_${Date.now()}_${Math.random()}` : formData.value.items[currentItemIndex.value].id,
      tempId: `item_${Date.now()}_${Math.random()}`,
      sequence: 0, // Will be updated when saving
      content: itemFormData.value.content,
      category: itemFormData.value.category,
      required: itemFormData.value.required,
    }
    
    if (itemFormMode.value === 'create') {
      formData.value.items.push(newItem)
      ElMessage.success('检查项添加成功')
    } else {
      formData.value.items[currentItemIndex.value] = newItem
      ElMessage.success('检查项更新成功')
    }
    
    // Update categories
    if (newItem.category && !itemCategories.value.includes(newItem.category)) {
      itemCategories.value.push(newItem.category)
    }
    
    itemFormVisible.value = false
  } catch (error) {
    console.error('Failed to save item:', error)
  }
}

const handleBatchAdd = () => {
  batchAddText.value = ''
  batchAddVisible.value = true
}

const handleBatchAddClose = () => {
  batchAddVisible.value = false
}

const handleBatchAddConfirm = () => {
  const newItems = batchPreviewItems.value.map((item, index) => ({
    id: `item_${Date.now()}_${index}`,
    tempId: `item_${Date.now()}_${index}`,
    sequence: 0,
    content: item.content,
    category: item.category,
    required: item.required,
  }))
  
  formData.value.items.push(...newItems)
  
  // Update categories
  newItems.forEach(item => {
    if (item.category && !itemCategories.value.includes(item.category)) {
      itemCategories.value.push(item.category)
    }
  })
  
  ElMessage.success(`成功添加 ${newItems.length} 个检查项`)
  batchAddVisible.value = false
}

const handleDragEnd = () => {
  // Items are automatically reordered by draggable
  ElMessage.success('检查项顺序已更新')
}

// 配置管理方法
const openDefectRuleConfig = () => {
  const templateId = props.template?.id
  const url = `/admin/defect-rules${templateId ? `?templateId=${templateId}` : ''}`
  window.open(url, '_blank')
}

const openTableConfig = () => {
  const templateId = props.template?.id
  const url = `/admin/table-config${templateId ? `?templateId=${templateId}` : ''}`
  window.open(url, '_blank')
}

const openButtonConfig = () => {
  const templateId = props.template?.id
  const url = `/admin/button-config${templateId ? `?templateId=${templateId}` : ''}`
  window.open(url, '_blank')
}
</script>

<style scoped>
.template-tabs {
  margin-bottom: 20px;
}

.template-tabs :deep(.el-tabs__content) {
  padding: 20px 0;
}

.template-tabs :deep(.el-tab-pane) {
  max-height: 60vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 20px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-weight: 600;
  color: #303133;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.section-actions {
  display: flex;
  gap: 8px;
}

.items-container {
  max-height: 400px;
  overflow-y: auto;
}

.item-card {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  background: #fafafa;
  transition: all 0.2s;
}

.item-card:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.item-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.drag-handle {
  cursor: move;
  color: #909399;
}

.item-sequence {
  font-weight: 600;
  color: #606266;
  min-width: 20px;
}

.item-actions {
  display: flex;
  gap: 4px;
}

.item-content {
  color: #303133;
  line-height: 1.5;
  padding-left: 28px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.batch-add-content {
  max-height: 60vh;
  overflow-y: auto;
}

.batch-preview {
  margin-top: 16px;
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

.batch-preview h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.preview-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
  background: #fafafa;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  font-size: 14px;
}

.preview-sequence {
  color: #909399;
  min-width: 20px;
}

.preview-content {
  flex: 1;
  color: #303133;
}

:deep(.el-card__header) {
  padding: 12px 16px;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

/* 高级配置样式 */
.advanced-config-content {
  padding: 16px;
}

.config-overview {
  margin-bottom: 24px;
}

.config-card {
  height: 100%;
  transition: all 0.3s ease;
}

.config-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.config-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.config-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
}

.config-info h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.config-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.config-actions {
  text-align: center;
}

.config-details {
  margin-top: 20px;
}

.rules-preview {
  max-height: 200px;
  overflow-y: auto;
}

.rule-preview-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.rule-name {
  font-weight: 600;
  color: #303133;
}

.rule-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.table-config-preview {
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.table-config-preview p {
  margin: 8px 0;
  color: #606266;
}

.buttons-preview {
  max-height: 200px;
  overflow-y: auto;
}

.button-group-preview {
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.button-group-preview h5 {
  margin: 0 0 8px 0;
  color: #303133;
  font-weight: 600;
}

.buttons-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
</style>