import request, { type RequestConfig } from '@/utils/request'

// Template related interfaces
export interface ChecklistTemplate {
  id: string
  name: string
  type: string
  version: string
  description: string
  category: string
  tags: string[]
  isActive: boolean
  createdTime: string
  updatedTime: string
  items: ChecklistItem[]
}

export interface ChecklistItem {
  id: string
  sequence: number
  content: string
  required: boolean
  category: string
  tags?: string[]
}

export interface CreateTemplateRequest {
  name: string
  type: string
  items: Omit<ChecklistItem, 'id'>[]
}

export interface UpdateTemplateRequest {
  name?: string
  type?: string
  items?: ChecklistItem[]
}

export interface ImportResult {
  success: boolean
  imported: number
  failed: number
  errors?: string[]
}

export interface TemplateListResponse {
  templates: ChecklistTemplate[]
  total: number
}

// 实际后端返回的原始格式（在request拦截器处理前）
export interface TemplateApiResponse {
  success: boolean
  data: ChecklistTemplate[]
  timestamp: number
}

// Template API service class
class TemplateApiService {
  private readonly baseUrl = '/templates'

  /**
   * Get all templates
   */
  async getTemplates(config?: RequestConfig): Promise<TemplateListResponse> {
    return request.get(this.baseUrl, {
      showLoading: true,
      ...config,
    })
  }

  /**
   * Get templates by type
   */
  async getTemplatesByType(type: string, config?: RequestConfig): Promise<ChecklistTemplate[]> {
    if (!type?.trim()) {
      throw new Error('模板类型不能为空')
    }

    return request.get(`${this.baseUrl}?type=${encodeURIComponent(type)}`, {
      showLoading: true,
      ...config,
    })
  }

  /**
   * Get template by ID
   */
  async getTemplateById(id: string, config?: RequestConfig): Promise<ChecklistTemplate> {
    if (!id?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.get(`${this.baseUrl}/${id}`, {
      showLoading: true,
      ...config,
    })
  }

  /**
   * Create new template
   */
  async createTemplate(
    template: CreateTemplateRequest,
    config?: RequestConfig,
  ): Promise<ChecklistTemplate> {
    // Validate required fields
    if (!template.name?.trim()) {
      throw new Error('模板名称不能为空')
    }
    if (!template.type?.trim()) {
      throw new Error('模板类型不能为空')
    }
    if (!template.items || template.items.length === 0) {
      throw new Error('检查项不能为空')
    }

    return request.post(this.baseUrl, template, {
      showLoading: true,
      showSuccess: true,
      successMessage: '模板创建成功',
      ...config,
    })
  }

  /**
   * Update template
   */
  async updateTemplate(
    id: string,
    template: UpdateTemplateRequest,
    config?: RequestConfig,
  ): Promise<ChecklistTemplate> {
    if (!id?.trim()) {
      throw new Error('模板ID不能为空')
    }

    // Validate fields if provided
    if (template.name !== undefined && !template.name.trim()) {
      throw new Error('模板名称不能为空')
    }
    if (template.type !== undefined && !template.type.trim()) {
      throw new Error('模板类型不能为空')
    }
    if (template.items !== undefined && template.items.length === 0) {
      throw new Error('检查项不能为空')
    }

    return request.put(`${this.baseUrl}/${id}`, template, {
      showLoading: true,
      showSuccess: true,
      successMessage: '模板更新成功',
      ...config,
    })
  }

  /**
   * Delete template
   */
  async deleteTemplate(id: string, config?: RequestConfig): Promise<void> {
    if (!id?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.delete(`${this.baseUrl}/${id}`, {
      showLoading: true,
      showSuccess: true,
      successMessage: '模板删除成功',
      ...config,
    })
  }

  /**
   * Import templates from Excel file
   */
  async importTemplatesFromExcel(file: File, config?: RequestConfig): Promise<ImportResult> {
    if (!file) {
      throw new Error('请选择要导入的文件')
    }

    // Validate file type
    const allowedTypes = [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ]
    if (!allowedTypes.includes(file.type)) {
      throw new Error('请选择有效的Excel文件 (.xls 或 .xlsx)')
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024
    if (file.size > maxSize) {
      throw new Error('文件大小不能超过10MB')
    }

    const formData = new FormData()
    formData.append('file', file)

    return request.post(`${this.baseUrl}/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      showLoading: true,
      showSuccess: true,
      successMessage: '文件导入成功',
      timeout: 60000, // 60 seconds for file upload
      ...config,
    })
  }

  /**
   * Export template to Excel
   */
  async exportTemplate(id: string, config?: RequestConfig): Promise<Blob> {
    if (!id?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.get(`${this.baseUrl}/${id}/export`, {
      responseType: 'blob',
      showLoading: true,
      ...config,
    })
  }

  /**
   * Duplicate template
   */
  async duplicateTemplate(id: string, newName: string, config?: RequestConfig): Promise<ChecklistTemplate> {
    if (!id?.trim()) {
      throw new Error('模板ID不能为空')
    }
    if (!newName?.trim()) {
      throw new Error('新模板名称不能为空')
    }

    return request.post(`${this.baseUrl}/${id}/duplicate`, { name: newName }, {
      showLoading: true,
      showSuccess: true,
      successMessage: '模板复制成功',
      ...config,
    })
  }

  /**
   * Get template types
   */
  async getTemplateTypes(config?: RequestConfig): Promise<string[]> {
    return request.get(`${this.baseUrl}/types`, {
      showLoading: false,
      ...config,
    })
  }
}

// Create and export service instance
const templateApi = new TemplateApiService()

// Export individual methods for backward compatibility
export const getTemplates = templateApi.getTemplates.bind(templateApi)
export const getTemplatesByType = templateApi.getTemplatesByType.bind(templateApi)
export const getTemplateById = templateApi.getTemplateById.bind(templateApi)
export const createTemplate = templateApi.createTemplate.bind(templateApi)
export const updateTemplate = templateApi.updateTemplate.bind(templateApi)
export const deleteTemplate = templateApi.deleteTemplate.bind(templateApi)
export const importTemplatesFromExcel = templateApi.importTemplatesFromExcel.bind(templateApi)
export const exportTemplate = templateApi.exportTemplate.bind(templateApi)
export const duplicateTemplate = templateApi.duplicateTemplate.bind(templateApi)
export const getTemplateTypes = templateApi.getTemplateTypes.bind(templateApi)

// Export service instance
export default templateApi
