<template>
  <div class="table-config-page">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">📊</div>
          <div class="header-text">
            <h1>动态表头配置</h1>
            <p>配置评审表格的列显示、排序和分组设置</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="showConfigDialog = true" type="primary">
            <el-icon><Setting /></el-icon>
            配置表头
          </el-button>
        </div>
      </div>
    </div>

    <div class="page-content">
      <!-- 模板选择 -->
      <div class="template-selector">
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <span>选择模板</span>
            </div>
          </template>
          
          <el-select
            v-model="selectedTemplateId"
            @change="handleTemplateChange"
            placeholder="选择要配置的模板"
            style="width: 300px"
          >
            <el-option
              v-for="template in templates"
              :key="template.id"
              :label="`${template.name} (${template.type})`"
              :value="template.id"
            />
          </el-select>
        </el-card>
      </div>

      <!-- 当前配置预览 -->
      <div v-if="selectedTemplateId && currentConfig" class="config-preview">
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <span>当前配置</span>
              <el-button size="small" @click="showConfigDialog = true">
                <el-icon><Edit /></el-icon>
                编辑配置
              </el-button>
            </div>
          </template>
          
          <div class="config-info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="配置名称">
                {{ currentConfig.name }}
              </el-descriptions-item>
              <el-descriptions-item label="列数量">
                {{ currentConfig.columns.length }}
              </el-descriptions-item>
              <el-descriptions-item label="分组字段">
                {{ currentConfig.groupBy || '无' }}
              </el-descriptions-item>
              <el-descriptions-item label="排序字段">
                {{ currentConfig.sortBy || '无' }}
              </el-descriptions-item>
            </el-descriptions>
            
            <h4 style="margin-top: 20px;">列配置详情</h4>
            <el-table :data="currentConfig.columns" stripe style="width: 100%">
              <el-table-column prop="label" label="列名称" width="150" />
              <el-table-column prop="key" label="字段键" width="120" />
              <el-table-column prop="type" label="类型" width="100">
                <template #default="{ row }">
                  <el-tag size="small">{{ row.type }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="width" label="宽度" width="80" />
              <el-table-column prop="visible" label="显示" width="80" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.visible ? 'success' : 'info'" size="small">
                    {{ row.visible ? '是' : '否' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="sortable" label="可排序" width="80" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.sortable ? 'success' : 'info'" size="small">
                    {{ row.sortable ? '是' : '否' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </div>

      <!-- 空状态 -->
      <el-empty
        v-if="!selectedTemplateId"
        description="请选择要配置的模板"
        :image-size="100"
      />
    </div>

    <!-- 表格配置对话框 -->
    <TableColumnConfig
      v-model="showConfigDialog"
      :current-config="currentConfig"
      @config-change="handleConfigChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Setting, Edit } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getTemplates, type ChecklistTemplate } from '@/api/template'
import TableColumnConfig from '@/components/review/TableColumnConfig.vue'
import { DEFAULT_TABLE_CONFIG, type TableViewConfig } from '@/types/table-config'
import {
  getTemplateTableConfig,
  saveTemplateTableConfig
} from '@/api/admin-config'

// Reactive data
const templates = ref<ChecklistTemplate[]>([])
const selectedTemplateId = ref('')
const currentConfig = ref<TableViewConfig | null>(null)
const showConfigDialog = ref(false)
const loading = ref(false)

// Computed
const templateFromQuery = computed(() => {
  const urlParams = new URLSearchParams(window.location.search)
  return urlParams.get('templateId')
})

// Methods
const loadTemplates = async () => {
  try {
    loading.value = true
    const response = await getTemplates()
    templates.value = Array.isArray(response) ? response : []
    
    // 如果URL中有templateId参数，自动选择
    const queryTemplateId = templateFromQuery.value
    if (queryTemplateId && templates.value.some(t => t.id === queryTemplateId)) {
      selectedTemplateId.value = queryTemplateId
      handleTemplateChange()
    }
  } catch (error) {
    console.error('Failed to load templates:', error)
    ElMessage.error('加载模板列表失败')
  } finally {
    loading.value = false
  }
}

const handleTemplateChange = async () => {
  if (!selectedTemplateId.value) {
    currentConfig.value = null
    return
  }
  
  try {
    // 暂时使用默认配置，后续后端 API 实现后改为 API 调用
    console.log('Loading table config for template:', selectedTemplateId.value)
    currentConfig.value = { ...DEFAULT_TABLE_CONFIG }
    ElMessage.success('已加载默认表头配置')
  } catch (error) {
    console.error('Failed to load table config:', error)
    currentConfig.value = { ...DEFAULT_TABLE_CONFIG }
  }
}

const handleConfigChange = async (config: TableViewConfig) => {
  if (!selectedTemplateId.value) return
  
  try {
    // 暂时模拟保存，后续后端 API 实现后改为 API 调用
    console.log('Saving table config for template:', selectedTemplateId.value, config)
    currentConfig.value = config
    ElMessage.success('表头配置保存成功（模拟）')
  } catch (error) {
    console.error('Failed to save table config:', error)
    ElMessage.error('保存表头配置失败')
  }
}

// Lifecycle
onMounted(() => {
  loadTemplates()
})
</script>

<style scoped>
.table-config-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
}

.page-header {
  background: #fff;
  margin-bottom: 24px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.header-content {
  padding: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-icon {
  font-size: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-text h1 {
  color: #303133;
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-text p {
  color: #606266;
  margin: 0;
  font-size: 16px;
  line-height: 1.6;
}

.page-content {
  background: #fff;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.template-selector {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.config-preview {
  margin-top: 24px;
}

.config-info h4 {
  color: #303133;
  margin-bottom: 12px;
}
</style>
