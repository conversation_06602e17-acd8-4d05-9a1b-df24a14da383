package com.checklist.repository;

import com.checklist.util.FileUtil;
import com.checklist.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * JSON 文件存储仓库基础实现类
 * 提供通用的 CRUD 操作实现
 * 
 * @param <T> 实体类型
 * @param <ID> 主键类型
 */
public abstract class BaseJsonFileRepository<T, ID> implements JsonFileRepository<T, ID> {
    
    // 文件锁映射，用于控制并发访问
    private static final Map<String, Object> fileLocks = new ConcurrentHashMap<>();
    
    protected final String dataDirectory;
    protected final String resolvedDataDirectory;
    protected final String fileExtension;
    protected final Class<T> entityClass;
    protected final TypeReference<List<T>> listTypeReference;

    /**
     * 构造函数
     *
     * @param dataDirectory 数据目录
     * @param entityClass 实体类
     * @param listTypeReference 列表类型引用
     */
    protected BaseJsonFileRepository(String dataDirectory, Class<T> entityClass,
                                   TypeReference<List<T>> listTypeReference) {
        this.dataDirectory = dataDirectory;
        this.resolvedDataDirectory = resolveDataDirectory(dataDirectory);
        this.fileExtension = ".json";
        this.entityClass = entityClass;
        this.listTypeReference = listTypeReference;

        // 确保数据目录存在
        try {
            FileUtil.createDirectories(this.resolvedDataDirectory);
        } catch (IOException e) {
            throw new RuntimeException("无法创建数据目录: " + this.resolvedDataDirectory +
                " (原始路径: " + dataDirectory + ")", e);
        }
    }
    
    /**
     * 获取实体ID的抽象方法
     * 
     * @param entity 实体
     * @return 实体ID
     */
    protected abstract ID getEntityId(T entity);
    
    /**
     * 设置实体ID的抽象方法
     * 
     * @param entity 实体
     * @param id 要设置的ID
     */
    protected abstract void setEntityId(T entity, ID id);
    
    /**
     * 生成新ID的抽象方法
     * 
     * @return 新的ID
     */
    protected abstract ID generateNewId();
    
    /**
     * 获取文件路径
     * 
     * @param id 实体ID
     * @return 文件路径
     */
    protected String getFilePath(ID id) {
        return dataDirectory + "/" + id + fileExtension;
    }
    
    /**
     * 获取文件锁对象
     * 
     * @param filePath 文件路径
     * @return 锁对象
     */
    protected Object getFileLock(String filePath) {
        return fileLocks.computeIfAbsent(filePath, k -> new Object());
    }
    
    @Override
    public T save(T entity) throws IOException {
        if (entity == null) {
            throw new IllegalArgumentException("实体不能为空");
        }
        
        ID id = getEntityId(entity);
        if (id == null) {
            id = generateNewId();
            setEntityId(entity, id);
        }
        
        String filePath = getFilePath(id);
        Object lock = getFileLock(filePath);
        
        synchronized (lock) {
            try {
                String jsonContent = JsonUtil.toJson(entity);
                FileUtil.safeWriteFile(filePath, jsonContent);
                return entity;
            } catch (Exception e) {
                throw new IOException("保存实体失败: " + id, e);
            }
        }
    }
    
    @Override
    public Optional<T> findById(ID id) throws IOException {
        if (id == null) {
            return Optional.empty();
        }
        
        String filePath = getFilePath(id);
        if (!FileUtil.fileExists(filePath)) {
            return Optional.empty();
        }
        
        Object lock = getFileLock(filePath);
        
        synchronized (lock) {
            try {
                String jsonContent = FileUtil.readFileWithLock(filePath);
                if (jsonContent == null || jsonContent.trim().isEmpty()) {
                    return Optional.empty();
                }
                
                T entity = JsonUtil.fromJson(jsonContent, entityClass);
                return Optional.ofNullable(entity);
            } catch (Exception e) {
                throw new IOException("读取实体失败: " + id, e);
            }
        }
    }
    
    @Override
    public List<T> findAll() throws IOException {
        List<T> result = new ArrayList<>();
        
        try {
            // 获取数据目录下所有JSON文件
            java.nio.file.Path dataPath = java.nio.file.Paths.get(dataDirectory);
            if (!java.nio.file.Files.exists(dataPath)) {
                return result;
            }
            
            try (java.nio.file.DirectoryStream<java.nio.file.Path> stream = 
                 java.nio.file.Files.newDirectoryStream(dataPath, "*" + fileExtension)) {
                
                for (java.nio.file.Path filePath : stream) {
                    try {
                        String jsonContent = FileUtil.readFileWithLock(filePath.toString());
                        if (jsonContent != null && !jsonContent.trim().isEmpty()) {
                            T entity = JsonUtil.fromJson(jsonContent, entityClass);
                            if (entity != null) {
                                result.add(entity);
                            }
                        }
                    } catch (Exception e) {
                        // 记录错误但继续处理其他文件
                        System.err.println("读取文件失败: " + filePath + ", 错误: " + e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            throw new IOException("读取所有实体失败", e);
        }
        
        return result;
    }
    
    @Override
    public boolean existsById(ID id) throws IOException {
        if (id == null) {
            return false;
        }
        
        String filePath = getFilePath(id);
        return FileUtil.fileExists(filePath);
    }
    
    @Override
    public void deleteById(ID id) throws IOException {
        if (id == null) {
            return;
        }
        
        String filePath = getFilePath(id);
        Object lock = getFileLock(filePath);
        
        synchronized (lock) {
            try {
                if (FileUtil.fileExists(filePath)) {
                    // 创建备份后删除
                    FileUtil.createBackup(filePath);
                    FileUtil.deleteFile(filePath);
                }
            } catch (Exception e) {
                throw new IOException("删除实体失败: " + id, e);
            }
        }
    }
    
    @Override
    public void delete(T entity) throws IOException {
        if (entity == null) {
            return;
        }
        
        ID id = getEntityId(entity);
        deleteById(id);
    }
    
    @Override
    public void deleteAll() throws IOException {
        try {
            java.nio.file.Path dataPath = java.nio.file.Paths.get(dataDirectory);
            if (!java.nio.file.Files.exists(dataPath)) {
                return;
            }
            
            try (java.nio.file.DirectoryStream<java.nio.file.Path> stream = 
                 java.nio.file.Files.newDirectoryStream(dataPath, "*" + fileExtension)) {
                
                for (java.nio.file.Path filePath : stream) {
                    try {
                        // 创建备份后删除
                        FileUtil.createBackup(filePath.toString());
                        FileUtil.deleteFile(filePath.toString());
                    } catch (Exception e) {
                        System.err.println("删除文件失败: " + filePath + ", 错误: " + e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            throw new IOException("删除所有实体失败", e);
        }
    }
    
    @Override
    public long count() throws IOException {
        try {
            java.nio.file.Path dataPath = java.nio.file.Paths.get(dataDirectory);
            if (!java.nio.file.Files.exists(dataPath)) {
                return 0;
            }
            
            long count = 0;
            try (java.nio.file.DirectoryStream<java.nio.file.Path> stream = 
                 java.nio.file.Files.newDirectoryStream(dataPath, "*" + fileExtension)) {
                
                for (java.nio.file.Path filePath : stream) {
                    count++;
                }
            }
            
            return count;
        } catch (Exception e) {
            throw new IOException("统计实体数量失败", e);
        }
    }
    
    /**
     * 根据条件查找实体
     * 
     * @param predicate 查找条件
     * @return 符合条件的实体列表
     * @throws IOException IO异常
     */
    public List<T> findBy(Function<T, Boolean> predicate) throws IOException {
        List<T> allEntities = findAll();
        List<T> result = new ArrayList<>();
        
        for (T entity : allEntities) {
            if (predicate.apply(entity)) {
                result.add(entity);
            }
        }
        
        return result;
    }
    
    /**
     * 根据条件查找第一个实体
     * 
     * @param predicate 查找条件
     * @return 符合条件的第一个实体的Optional包装
     * @throws IOException IO异常
     */
    public Optional<T> findFirstBy(Function<T, Boolean> predicate) throws IOException {
        List<T> allEntities = findAll();
        
        for (T entity : allEntities) {
            if (predicate.apply(entity)) {
                return Optional.of(entity);
            }
        }
        
        return Optional.empty();
    }
}