package com.checklist.repository;

import com.checklist.model.ChecklistTemplate;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 检查单模板仓库实现类
 */
public class ChecklistTemplateRepository extends BaseJsonFileRepository<ChecklistTemplate, String> {
    
    private static final String DATA_DIRECTORY = "data/templates";
    
    public ChecklistTemplateRepository() {
        super(DATA_DIRECTORY, ChecklistTemplate.class, new TypeReference<List<ChecklistTemplate>>() {});
    }
    
    /**
     * 测试用构造函数，允许指定数据目录
     * 
     * @param dataDirectory 数据目录
     */
    public ChecklistTemplateRepository(String dataDirectory) {
        super(dataDirectory, ChecklistTemplate.class, new TypeReference<List<ChecklistTemplate>>() {});
    }
    
    @Override
    protected String getEntityId(ChecklistTemplate entity) {
        return entity.getId();
    }
    
    @Override
    protected void setEntityId(ChecklistTemplate entity, String id) {
        entity.setId(id);
    }
    
    @Override
    protected String generateNewId() {
        return "template_" + UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 根据类型查找模板
     * 
     * @param type 模板类型
     * @return 符合条件的模板列表
     * @throws IOException IO异常
     */
    public List<ChecklistTemplate> findByType(String type) throws IOException {
        if (type == null || type.trim().isEmpty()) {
            throw new IllegalArgumentException("模板类型不能为空");
        }
        
        return findBy(template -> type.equals(template.getType()));
    }
    
    /**
     * 根据名称查找模板
     * 
     * @param name 模板名称
     * @return 符合条件的模板的Optional包装
     * @throws IOException IO异常
     */
    public Optional<ChecklistTemplate> findByName(String name) throws IOException {
        if (name == null || name.trim().isEmpty()) {
            return Optional.empty();
        }
        
        return findFirstBy(template -> name.equals(template.getName()));
    }
    
    /**
     * 根据类型和版本查找模板
     * 
     * @param type 模板类型
     * @param version 模板版本
     * @return 符合条件的模板的Optional包装
     * @throws IOException IO异常
     */
    public Optional<ChecklistTemplate> findByTypeAndVersion(String type, String version) throws IOException {
        if (type == null || type.trim().isEmpty() || version == null || version.trim().isEmpty()) {
            return Optional.empty();
        }
        
        return findFirstBy(template -> 
            type.equals(template.getType()) && version.equals(template.getVersion()));
    }
    
    /**
     * 获取指定类型的最新版本模板
     * 
     * @param type 模板类型
     * @return 最新版本模板的Optional包装
     * @throws IOException IO异常
     */
    public Optional<ChecklistTemplate> findLatestByType(String type) throws IOException {
        if (type == null || type.trim().isEmpty()) {
            return Optional.empty();
        }
        
        List<ChecklistTemplate> templates = findByType(type);
        if (templates.isEmpty()) {
            return Optional.empty();
        }
        
        // 按更新时间排序，获取最新的模板
        // 由于时间字段现在是Object类型，暂时按创建顺序返回第一个
        // 在实际使用中，可以根据需要实现更复杂的排序逻辑
        
        return Optional.of(templates.get(0));
    }
    
    /**
     * 检查模板名称是否已存在
     * 
     * @param name 模板名称
     * @param excludeId 排除的模板ID（用于更新时检查）
     * @return 是否存在
     * @throws IOException IO异常
     */
    public boolean existsByName(String name, String excludeId) throws IOException {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        
        Optional<ChecklistTemplate> existing = findByName(name);
        if (!existing.isPresent()) {
            return false;
        }
        
        // 如果指定了排除ID，检查是否为同一个模板
        if (excludeId != null && excludeId.equals(existing.get().getId())) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查模板名称是否已存在
     * 
     * @param name 模板名称
     * @return 是否存在
     * @throws IOException IO异常
     */
    public boolean existsByName(String name) throws IOException {
        return existsByName(name, null);
    }
}