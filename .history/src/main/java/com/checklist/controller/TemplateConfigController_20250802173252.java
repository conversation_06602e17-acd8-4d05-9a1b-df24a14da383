package com.checklist.controller;

import com.checklist.model.ChecklistTemplate;
import com.checklist.service.ChecklistTemplateService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 模板配置管理控制器
 * 提供统一的模板配置管理接口，以模板ID为维度管理所有配置
 */
@RestController
@RequestMapping("/template-config")
public class TemplateConfigController {

    private final ChecklistTemplateService templateService;
    private final FileStorageService fileStorageService;

    public TemplateConfigController(ChecklistTemplateService templateService, FileStorageService fileStorageService) {
        this.templateService = templateService;
        this.fileStorageService = fileStorageService;
    }

    /**
     * 加载模板的完整配置
     * GET /api/template-config/{templateId}
     */
    @GetMapping("/{templateId}")
    public ResponseEntity<Map<String, Object>> loadTemplateConfig(@PathVariable String templateId) {
        try {
            // 参数验证
            if (templateId == null || templateId.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 获取模板基本信息
            Optional<ChecklistTemplate> templateOpt = templateService.getTemplateById(templateId.trim());
            if (!templateOpt.isPresent()) {
                Map<String, Object> errorResponse = createErrorResponse("TEMPLATE_NOT_FOUND", 
                    "模板不存在: " + templateId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
            }

            ChecklistTemplate template = templateOpt.get();

            // 构建完整配置响应
            Map<String, Object> configResponse = new HashMap<>();
            configResponse.put("template", template);

            // 暂时返回空的扩展配置，因为当前所有数据都在同一个JSON文件中
            // 后续如果需要扩展配置，可以在模板JSON文件中添加相应字段
            configResponse.put("tableConfig", null);
            configResponse.put("defectRules", new Object[0]);
            configResponse.put("statusButtons", new Object[0]);

            Map<String, Object> response = createSuccessResponse(configResponse);
            return ResponseEntity.ok(response);

        } catch (IOException e) {
            Map<String, Object> errorResponse = createErrorResponse("TEMPLATE_READ_ERROR", 
                "读取模板配置失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "获取模板配置时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 保存模板的完整配置
     * PUT /api/template-config/{templateId}
     */
    @PutMapping("/{templateId}")
    public ResponseEntity<Map<String, Object>> saveTemplateConfig(
            @PathVariable String templateId,
            @RequestBody Map<String, Object> configData) {
        try {
            // 参数验证
            if (templateId == null || templateId.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            if (configData == null) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_REQUEST_BODY", 
                    "请求体不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 当前只处理基本模板信息的更新
            // 扩展配置（tableConfig、defectRules、statusButtons）暂时不处理
            // 因为当前所有数据都存储在同一个模板JSON文件中

            if (configData.containsKey("basicInfo") || configData.containsKey("items")) {
                // 更新模板基本信息和检查项
                updateTemplateFromConfig(templateId, configData);
            }

            Map<String, Object> response = createSuccessResponse("模板配置保存成功");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "保存模板配置时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 获取模板的表格配置
     * GET /api/template-config/{templateId}/table-config
     */
    @GetMapping("/{templateId}/table-config")
    public ResponseEntity<Map<String, Object>> getTemplateTableConfig(@PathVariable String templateId) {
        try {
            // 参数验证
            if (templateId == null || templateId.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 暂时返回空配置，因为表格配置存储在模板JSON文件中
            // TODO: 如果需要单独的表格配置，可以从模板文件中提取
            Map<String, Object> response = createSuccessResponse(null);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "获取表格配置时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 保存模板的表格配置
     * PUT /api/template-config/{templateId}/table-config
     */
    @PutMapping("/{templateId}/table-config")
    public ResponseEntity<Map<String, Object>> saveTemplateTableConfig(
            @PathVariable String templateId,
            @RequestBody Map<String, Object> tableConfig) {
        try {
            // 参数验证
            if (templateId == null || templateId.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 暂时不处理表格配置保存，因为配置存储在模板JSON文件中
            // TODO: 如果需要单独保存表格配置，需要实现相应逻辑

            Map<String, Object> response = createSuccessResponse("表格配置保存成功");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "保存表格配置时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 获取模板的缺陷规则配置
     * GET /api/template-config/{templateId}/defect-rules
     */
    @GetMapping("/{templateId}/defect-rules")
    public ResponseEntity<Map<String, Object>> getTemplateDefectRules(@PathVariable String templateId) {
        try {
            // 参数验证
            if (templateId == null || templateId.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 暂时返回空数组，因为缺陷规则存储在模板JSON文件中
            // TODO: 如果需要单独的缺陷规则，可以从模板文件中提取
            Map<String, Object> response = createSuccessResponse(new Object[0]);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "获取缺陷规则时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 保存模板的缺陷规则配置
     * PUT /api/template-config/{templateId}/defect-rules
     */
    @PutMapping("/{templateId}/defect-rules")
    public ResponseEntity<Map<String, Object>> saveTemplateDefectRules(
            @PathVariable String templateId,
            @RequestBody Map<String, Object> defectRulesData) {
        try {
            // 参数验证
            if (templateId == null || templateId.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 暂时不处理缺陷规则保存，因为规则存储在模板JSON文件中
            // TODO: 如果需要单独保存缺陷规则，需要实现相应逻辑

            Map<String, Object> response = createSuccessResponse("缺陷规则保存成功");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "保存缺陷规则时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 获取模板的状态按钮配置
     * GET /api/template-config/{templateId}/status-buttons
     */
    @GetMapping("/{templateId}/status-buttons")
    public ResponseEntity<Map<String, Object>> getTemplateStatusButtons(@PathVariable String templateId) {
        try {
            // 参数验证
            if (templateId == null || templateId.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 暂时返回空数组，因为状态按钮配置存储在模板JSON文件中
            // TODO: 如果需要单独的状态按钮配置，可以从模板文件中提取
            Map<String, Object> response = createSuccessResponse(new Object[0]);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "获取状态按钮配置时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 保存模板的状态按钮配置
     * PUT /api/template-config/{templateId}/status-buttons
     */
    @PutMapping("/{templateId}/status-buttons")
    public ResponseEntity<Map<String, Object>> saveTemplateStatusButtons(
            @PathVariable String templateId,
            @RequestBody Map<String, Object> statusButtonsData) {
        try {
            // 参数验证
            if (templateId == null || templateId.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 暂时不处理状态按钮配置保存，因为配置存储在模板JSON文件中
            // TODO: 如果需要单独保存状态按钮配置，需要实现相应逻辑

            Map<String, Object> response = createSuccessResponse("状态按钮配置保存成功");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "保存状态按钮配置时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 创建成功响应
     */
    private Map<String, Object> createSuccessResponse(Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", data);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String errorCode, String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorCode", errorCode);
        response.put("message", message);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    /**
     * 从配置数据更新模板
     */
    private void updateTemplateFromConfig(String templateId, Map<String, Object> configData) throws IOException {
        Optional<ChecklistTemplate> templateOpt = templateService.getTemplateById(templateId);
        if (!templateOpt.isPresent()) {
            throw new IOException("模板不存在: " + templateId);
        }

        ChecklistTemplate template = templateOpt.get();
        boolean hasChanges = false;

        // 更新基本信息
        if (configData.containsKey("basicInfo")) {
            @SuppressWarnings("unchecked")
            Map<String, Object> basicInfo = (Map<String, Object>) configData.get("basicInfo");

            if (basicInfo.containsKey("name")) {
                template.setName((String) basicInfo.get("name"));
                hasChanges = true;
            }
            if (basicInfo.containsKey("type")) {
                template.setType((String) basicInfo.get("type"));
                hasChanges = true;
            }
            if (basicInfo.containsKey("version")) {
                template.setVersion((String) basicInfo.get("version"));
                hasChanges = true;
            }
        }

        // 更新检查项
        if (configData.containsKey("items")) {
            // 这里需要将前端的 ExtendedChecklistItem 转换为后端的 ChecklistItem
            // 暂时跳过，因为需要复杂的数据转换逻辑
            // TODO: 实现检查项更新逻辑
        }

        // 如果有变更，保存模板
        if (hasChanges) {
            templateService.updateTemplate(templateId, template);
        }
    }
}
