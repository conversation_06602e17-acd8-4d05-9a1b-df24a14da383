package com.checklist.controller;

import com.checklist.model.ChecklistTemplate;
import com.checklist.service.ChecklistTemplateService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 检查单模板管理控制器
 * 提供模板管理的 REST API 接口
 */
@RestController
@RequestMapping("/templates")
public class ChecklistTemplateController {

    private final ChecklistTemplateService templateService;

    public ChecklistTemplateController(ChecklistTemplateService templateService) {
        this.templateService = templateService;
    }
    
    /**
     * 获取所有模板
     * GET /api/templates
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getAllTemplates() {
        try {
            List<ChecklistTemplate> templates = templateService.getAllTemplates();
            Map<String, Object> response = createSuccessResponse(templates);
            return ResponseEntity.ok(response);
        } catch (IOException e) {
            Map<String, Object> errorResponse = createErrorResponse("TEMPLATE_READ_ERROR", 
                "读取模板列表失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "获取模板列表时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * 根据类型获取模板
     * GET /api/templates/{type}
     */
    @GetMapping("/{type}")
    public ResponseEntity<Map<String, Object>> getTemplatesByType(@PathVariable String type) {
        try {
            // 参数验证
            if (type == null || type.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板类型不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            List<ChecklistTemplate> templates = templateService.getTemplatesByType(type.trim());
            Map<String, Object> response = createSuccessResponse(templates);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        } catch (IOException e) {
            Map<String, Object> errorResponse = createErrorResponse("TEMPLATE_READ_ERROR", 
                "读取模板失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "获取模板时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * 创建新模板
     * POST /api/templates
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createTemplate(@RequestBody ChecklistTemplate template) {
        try {
            // 基本参数验证
            if (template == null) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_REQUEST_BODY", 
                    "请求体不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            ChecklistTemplate createdTemplate = templateService.createTemplate(template);
            Map<String, Object> response = createSuccessResponse(createdTemplate);
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> errorResponse = createErrorResponse("VALIDATION_ERROR", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        } catch (IOException e) {
            Map<String, Object> errorResponse = createErrorResponse("TEMPLATE_SAVE_ERROR", 
                "保存模板失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "创建模板时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * 更新模板
     * PUT /api/templates/{id}
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateTemplate(@PathVariable String id, 
                                                             @RequestBody ChecklistTemplate template) {
        try {
            // 基本参数验证
            if (id == null || id.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            if (template == null) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_REQUEST_BODY", 
                    "请求体不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            ChecklistTemplate updatedTemplate = templateService.updateTemplate(id.trim(), template);
            Map<String, Object> response = createSuccessResponse(updatedTemplate);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> errorResponse = createErrorResponse("VALIDATION_ERROR", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        } catch (IOException e) {
            Map<String, Object> errorResponse = createErrorResponse("TEMPLATE_UPDATE_ERROR", 
                "更新模板失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "更新模板时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * 删除模板
     * DELETE /api/templates/{id}
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteTemplate(@PathVariable String id) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            templateService.deleteTemplate(id.trim());
            Map<String, Object> response = createSuccessResponse("模板删除成功");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> errorResponse = createErrorResponse("VALIDATION_ERROR", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        } catch (IOException e) {
            Map<String, Object> errorResponse = createErrorResponse("TEMPLATE_DELETE_ERROR", 
                "删除模板失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "删除模板时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * 根据ID获取单个模板
     * GET /api/templates/detail/{id}?includeConfig=true 返回完整配置
     * GET /api/templates/detail/{id} 返回基本信息
     */
    @GetMapping("/detail/{id}")
    public ResponseEntity<Map<String, Object>> getTemplateById(
            @PathVariable String id,
            @RequestParam(value = "includeConfig", defaultValue = "false") boolean includeConfig) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            Optional<ChecklistTemplate> templateOpt = templateService.getTemplateById(id.trim());
            if (templateOpt.isPresent()) {
                ChecklistTemplate template = templateOpt.get();

                if (includeConfig) {
                    // 返回完整配置格式（兼容前端的 template-config 接口期望）
                    Map<String, Object> configResponse = new HashMap<>();
                    configResponse.put("template", template);
                    configResponse.put("tableConfig", template.getTableConfig());
                    configResponse.put("defectRules", template.getDefectRules() != null ? template.getDefectRules() : new Object[0]);
                    configResponse.put("statusButtons", template.getStatusButtons() != null ? template.getStatusButtons() : new Object[0]);

                    Map<String, Object> response = createSuccessResponse(configResponse);
                    return ResponseEntity.ok(response);
                } else {
                    // 返回基本模板信息
                    Map<String, Object> response = createSuccessResponse(template);
                    return ResponseEntity.ok(response);
                }
            } else {
                Map<String, Object> errorResponse = createErrorResponse("TEMPLATE_NOT_FOUND", 
                    "模板不存在: " + id);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
            }
        } catch (IOException e) {
            Map<String, Object> errorResponse = createErrorResponse("TEMPLATE_READ_ERROR", 
                "读取模板失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "获取模板时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * 保存模板配置
     * PUT /api/templates/config/{id}
     */
    @PutMapping("/config/{id}")
    public ResponseEntity<Map<String, Object>> saveTemplateConfig(
            @PathVariable String id,
            @RequestBody Map<String, Object> configData) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER",
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            if (configData == null) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_REQUEST_BODY",
                    "请求体不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 获取现有模板
            Optional<ChecklistTemplate> templateOpt = templateService.getTemplateById(id.trim());
            if (!templateOpt.isPresent()) {
                Map<String, Object> errorResponse = createErrorResponse("TEMPLATE_NOT_FOUND",
                    "模板不存在: " + id);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
            }

            ChecklistTemplate template = templateOpt.get();

            // 更新配置数据
            if (configData.containsKey("tableConfig")) {
                template.setTableConfig(configData.get("tableConfig"));
            }

            if (configData.containsKey("defectRules")) {
                @SuppressWarnings("unchecked")
                List<Object> defectRules = (List<Object>) configData.get("defectRules");
                template.setDefectRules(defectRules);
            }

            if (configData.containsKey("statusButtons")) {
                @SuppressWarnings("unchecked")
                List<Object> statusButtons = (List<Object>) configData.get("statusButtons");
                template.setStatusButtons(statusButtons);
            }

            // 更新基本信息
            if (configData.containsKey("basicInfo")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> basicInfo = (Map<String, Object>) configData.get("basicInfo");
                if (basicInfo.containsKey("name")) {
                    template.setName((String) basicInfo.get("name"));
                }
                if (basicInfo.containsKey("type")) {
                    template.setType((String) basicInfo.get("type"));
                }
                if (basicInfo.containsKey("description")) {
                    template.setDescription((String) basicInfo.get("description"));
                }
                if (basicInfo.containsKey("category")) {
                    template.setCategory((String) basicInfo.get("category"));
                }
                if (basicInfo.containsKey("tags")) {
                    @SuppressWarnings("unchecked")
                    List<String> tags = (List<String>) basicInfo.get("tags");
                    template.setTags(tags);
                }
                if (basicInfo.containsKey("isActive")) {
                    template.setIsActive((Boolean) basicInfo.get("isActive"));
                }
            }

            // 更新检查项
            if (configData.containsKey("items")) {
                @SuppressWarnings("unchecked")
                List<ChecklistItem> items = (List<ChecklistItem>) configData.get("items");
                template.setItems(items);
            }

            // 保存更新后的模板
            ChecklistTemplate updatedTemplate = templateService.updateTemplate(template);
            Map<String, Object> response = createSuccessResponse("模板配置保存成功");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR",
                "保存模板配置时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Excel 导入模板
     * POST /api/templates/import
     */
    @PostMapping("/import")
    public ResponseEntity<Map<String, Object>> importTemplateFromExcel(
            @RequestParam("file") MultipartFile file,
            @RequestParam("templateName") String templateName,
            @RequestParam("templateType") String templateType) {
        
        try {
            // 基本参数验证
            if (file == null || file.isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "Excel 文件不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            if (templateName == null || templateName.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板名称不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            if (templateType == null || templateType.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板类型不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            // 导入模板
            ChecklistTemplate importedTemplate = templateService.importTemplateFromExcel(
                file, templateName.trim(), templateType.trim());
            
            Map<String, Object> response = createSuccessResponse(importedTemplate);
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
            
        } catch (IllegalArgumentException e) {
            Map<String, Object> errorResponse = createErrorResponse("VALIDATION_ERROR", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        } catch (IOException e) {
            Map<String, Object> errorResponse = createErrorResponse("EXCEL_IMPORT_ERROR", 
                "Excel 导入失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "Excel 导入时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * 创建成功响应
     */
    private Map<String, Object> createSuccessResponse(Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", data);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
    
    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String errorCode, String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorCode", errorCode);
        response.put("message", message);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
}