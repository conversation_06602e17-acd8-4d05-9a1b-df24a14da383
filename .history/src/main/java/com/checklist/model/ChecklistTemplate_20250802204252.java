package com.checklist.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 检查单模板数据模型
 */
public class ChecklistTemplate {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("type")
    private String type;
    
    @JsonProperty("version")
    private String version;
    
    @JsonProperty("createdTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    @JsonProperty("updatedTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;
    
    @JsonProperty("description")
    private String description;

    @JsonProperty("category")
    private String category;

    @JsonProperty("tags")
    private List<String> tags;

    @JsonProperty("isActive")
    private Boolean isActive;

    @JsonProperty("items")
    private List<ChecklistItem> items;

    @JsonProperty("tableConfig")
    private Object tableConfig;

    @JsonProperty("defectRules")
    private List<Object> defectRules;

    @JsonProperty("statusButtons")
    private List<Object> statusButtons;
    
    // 默认构造函数
    public ChecklistTemplate() {}
    
    // 全参构造函数
    public ChecklistTemplate(String id, String name, String type, String version,
                           LocalDateTime createdTime, LocalDateTime updatedTime,
                           List<ChecklistItem> items, String description, String category,
                           List<String> tags, Boolean isActive, Object tableConfig,
                           List<Object> defectRules, List<Object> statusButtons) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.version = version;
        this.createdTime = createdTime;
        this.updatedTime = updatedTime;
        this.items = items;
        this.description = description;
        this.category = category;
        this.tags = tags;
        this.isActive = isActive;
        this.tableConfig = tableConfig;
        this.defectRules = defectRules;
        this.statusButtons = statusButtons;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getVersion() {
        return version;
    }
    
    public void setVersion(String version) {
        this.version = version;
    }
    
    public Object getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Object createdTime) {
        this.createdTime = createdTime;
    }

    public Object getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Object updatedTime) {
        this.updatedTime = updatedTime;
    }
    
    public List<ChecklistItem> getItems() {
        return items;
    }
    
    public void setItems(List<ChecklistItem> items) {
        this.items = items;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Object getTableConfig() {
        return tableConfig;
    }

    public void setTableConfig(Object tableConfig) {
        this.tableConfig = tableConfig;
    }

    public List<Object> getDefectRules() {
        return defectRules;
    }

    public void setDefectRules(List<Object> defectRules) {
        this.defectRules = defectRules;
    }

    public List<Object> getStatusButtons() {
        return statusButtons;
    }

    public void setStatusButtons(List<Object> statusButtons) {
        this.statusButtons = statusButtons;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ChecklistTemplate that = (ChecklistTemplate) o;
        return Objects.equals(id, that.id) &&
               Objects.equals(name, that.name) &&
               Objects.equals(type, that.type) &&
               Objects.equals(version, that.version);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id, name, type, version);
    }
    
    @Override
    public String toString() {
        return "ChecklistTemplate{" +
               "id='" + id + '\'' +
               ", name='" + name + '\'' +
               ", type='" + type + '\'' +
               ", version='" + version + '\'' +
               ", description='" + description + '\'' +
               ", category='" + category + '\'' +
               ", tags=" + tags +
               ", isActive=" + isActive +
               ", createdTime=" + createdTime +
               ", updatedTime=" + updatedTime +
               ", items=" + items +
               ", tableConfig=" + tableConfig +
               ", defectRules=" + defectRules +
               ", statusButtons=" + statusButtons +
               '}';
    }
}